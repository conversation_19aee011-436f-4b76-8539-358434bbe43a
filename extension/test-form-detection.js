// 测试表单检测功能的简单脚本
// 在浏览器控制台中运行此脚本来测试修复效果

console.log('开始测试表单检测功能...');

// 模拟表单检测测试
async function testFormDetection() {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab?.id) {
      console.error('无法获取当前标签页');
      return;
    }

    console.log('测试标签页:', tab.url);

    // 测试PING消息
    console.log('1. 测试PING消息...');
    const pingResult = await new Promise((resolve) => {
      chrome.tabs.sendMessage(tab.id, { type: 'PING' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ success: false, error: chrome.runtime.lastError.message });
        } else {
          resolve(response);
        }
      });
    });
    console.log('PING结果:', pingResult);

    // 测试表单检测
    console.log('2. 测试表单检测...');
    const detectResult = await new Promise((resolve) => {
      chrome.tabs.sendMessage(tab.id, { type: 'DETECT_FORMS' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ success: false, error: chrome.runtime.lastError.message });
        } else {
          resolve(response);
        }
      });
    });
    console.log('表单检测结果:', detectResult);

    // 测试多次连续检测（模拟重试）
    console.log('3. 测试连续检测（模拟重试场景）...');
    for (let i = 1; i <= 3; i++) {
      console.log(`第${i}次检测...`);
      const result = await new Promise((resolve) => {
        chrome.tabs.sendMessage(tab.id, { type: 'DETECT_FORMS' }, (response) => {
          if (chrome.runtime.lastError) {
            resolve({ success: false, error: chrome.runtime.lastError.message });
          } else {
            resolve(response);
          }
        });
      });
      console.log(`第${i}次结果:`, result.success ? '成功' : '失败');
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('测试完成！');

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
testFormDetection();

// 导出测试函数供手动调用
window.testFormDetection = testFormDetection;

console.log('测试脚本已加载。可以调用 testFormDetection() 重新运行测试。');
