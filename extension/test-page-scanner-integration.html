<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面扫描器集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button, .submit-link {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        button:hover, .submit-link:hover {
            background-color: #0056b3;
        }
        .test-instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .dynamic-content {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>页面扫描器集成测试页面</h1>
    
    <div class="test-instructions">
        <h3>测试说明</h3>
        <ol>
            <li>安装并启用 LinkTrackPro 扩展</li>
            <li>打开扩展的侧边栏</li>
            <li>切换到"探索模式"标签</li>
            <li>观察页面扫描器是否自动检测到表单和提交链接</li>
            <li>点击"检测表单"按钮测试手动检测</li>
            <li>点击"全部扫描"按钮测试综合扫描</li>
            <li>点击"添加动态表单"按钮测试页面结构变化检测</li>
        </ol>
    </div>

    <!-- 测试表单1：项目提交表单 -->
    <div class="test-section">
        <h2>项目提交表单</h2>
        <form action="/submit-project" method="post">
            <div class="form-group">
                <label for="project-name">项目名称</label>
                <input type="text" id="project-name" name="projectName" required>
            </div>
            
            <div class="form-group">
                <label for="project-url">项目网址</label>
                <input type="url" id="project-url" name="projectUrl" required>
            </div>
            
            <div class="form-group">
                <label for="project-description">项目描述</label>
                <textarea id="project-description" name="projectDescription" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="project-category">项目类别</label>
                <select id="project-category" name="projectCategory" required>
                    <option value="">请选择类别</option>
                    <option value="web">网站应用</option>
                    <option value="mobile">移动应用</option>
                    <option value="tool">开发工具</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <button type="submit">提交项目</button>
        </form>
    </div>

    <!-- 测试表单2：联系表单 -->
    <div class="test-section">
        <h2>联系我们</h2>
        <form action="/contact" method="post">
            <div class="form-group">
                <label for="contact-name">姓名</label>
                <input type="text" id="contact-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="contact-email">邮箱</label>
                <input type="email" id="contact-email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="contact-message">消息</label>
                <textarea id="contact-message" name="message" required></textarea>
            </div>
            
            <button type="submit">发送消息</button>
        </form>
    </div>

    <!-- 提交相关链接 -->
    <div class="test-section">
        <h2>提交相关链接</h2>
        <p>这些链接应该被页面扫描器检测到：</p>
        <a href="/submit" class="submit-link">提交作品</a>
        <a href="/apply" class="submit-link">申请加入</a>
        <a href="/register" class="submit-link">注册账号</a>
        <a href="/contact-us" class="submit-link">联系我们</a>
        <a href="/feedback" class="submit-link">意见反馈</a>
    </div>

    <!-- 动态内容测试 -->
    <div class="test-section">
        <h2>动态内容测试</h2>
        <div class="dynamic-content" id="dynamic-content">
            <p>点击下面的按钮添加动态表单，测试页面结构变化检测</p>
            <button onclick="addDynamicForm()">添加动态表单</button>
        </div>
    </div>

    <script>
        function addDynamicForm() {
            const container = document.getElementById('dynamic-content');
            const formHtml = `
                <div style="border: 1px solid #ddd; padding: 20px; margin: 10px 0; background: #f9f9f9;">
                    <h3>动态添加的表单</h3>
                    <form action="/dynamic-submit" method="post">
                        <div style="margin-bottom: 15px;">
                            <label>动态字段：</label>
                            <input type="text" name="dynamicField" style="width: 100%; padding: 8px; margin-top: 5px;">
                        </div>
                        <button type="submit">提交动态表单</button>
                    </form>
                </div>
            `;
            container.innerHTML += formHtml;
            
            // 触发页面结构变化事件
            console.log('动态表单已添加，应该触发自动检测');
        }

        // 模拟页面加载完成
        window.addEventListener('load', function() {
            console.log('页面加载完成，应该触发自动表单检测');
        });

        // 模拟URL变化（用于测试）
        function simulateUrlChange() {
            history.pushState({}, '', window.location.pathname + '?test=' + Date.now());
            console.log('URL已变化，应该触发自动检测');
        }

        // 添加测试按钮
        setTimeout(() => {
            const testButton = document.createElement('button');
            testButton.textContent = '模拟URL变化';
            testButton.onclick = simulateUrlChange;
            testButton.style.position = 'fixed';
            testButton.style.top = '10px';
            testButton.style.right = '10px';
            testButton.style.zIndex = '1000';
            document.body.appendChild(testButton);
        }, 1000);
    </script>
</body>
</html>
