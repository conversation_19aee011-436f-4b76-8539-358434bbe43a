<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据转换修复</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007acc;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .success {
            border-left-color: #27ae60;
            background: #f2fdf2;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <h1>🧪 流量数据转换修复测试</h1>
    
    <div class="test-section">
        <h2>问题描述</h2>
        <p>原始API响应包含有效数据，但转换后所有流量指标都变成0值：</p>
        <ul>
            <li>Visits: "360" → totalVisits: 0</li>
            <li>BounceRate: "0.44218143188900894" → bounceRate: 0</li>
            <li>PagePerVisit: "1.022190881727478" → pagesPerVisit: 0</li>
            <li>国家代码: 392, 410 → 显示为数字而非国家名称</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>模拟API响应数据</h2>
        <pre id="mockData"></pre>
        <button onclick="runTest()">🚀 运行转换测试</button>
        <button onclick="clearResults()">🗑️ 清除结果</button>
    </div>

    <div id="results"></div>

    <script>
        // 模拟API响应数据（基于真实API结构）
        const mockApiResponse = {
            "Version": 1,
            "SiteName": "aimcp.info",
            "Description": null,
            "TopCountryShares": [
                {
                    "Country": 392,
                    "CountryCode": "JP",
                    "Value": 0.8100900590613417
                },
                {
                    "Country": 410,
                    "CountryCode": "KR",
                    "Value": 0.1899099409386585
                }
            ],
            "Engagments": {
                "BounceRate": "0.44218143188900894",
                "Month": "6",
                "Year": "2025",
                "PagePerVisit": "1.022190881727478",
                "Visits": "360",
                "TimeOnSite": "0"
            },
            "EstimatedMonthlyVisits": {
                "2025-04-01": 595,
                "2025-05-01": 594,
                "2025-06-01": 360
            },
            "GlobalRank": {
                "Rank": null
            },
            "CountryRank": {
                "Country": null,
                "CountryCode": null,
                "Rank": null
            },
            "CategoryRank": {
                "Rank": null,
                "Category": null
            },
            "IsSmall": true,
            "Category": ""
        };

        // 显示模拟数据
        document.getElementById('mockData').textContent = JSON.stringify(mockApiResponse, null, 2);

        // 模拟SimilarWebService的关键方法
        class TestSimilarWebService {
            extractNumber(obj, fieldNames, divisor = 1) {
                for (const fieldName of fieldNames) {
                    const value = this.getNestedValue(obj, fieldName);
                    console.log(`提取字段 ${fieldName}:`, value, typeof value);
                    
                    if (typeof value === 'number' && !Number.isNaN(value)) {
                        const result = value / divisor;
                        console.log(`数字值转换: ${value} / ${divisor} = ${result}`);
                        return result;
                    }
                    
                    if (typeof value === 'string' && value.trim() !== '') {
                        // 移除逗号、百分号、空格等字符
                        const cleanValue = value.replace(/[,%\s]/g, '');
                        const parsed = parseFloat(cleanValue);
                        console.log(`字符串值转换: "${value}" -> "${cleanValue}" -> ${parsed}`);
                        
                        if (!Number.isNaN(parsed) && isFinite(parsed)) {
                            const result = parsed / divisor;
                            console.log(`最终结果: ${parsed} / ${divisor} = ${result}`);
                            return result;
                        }
                    }
                }
                console.log(`未找到有效值，返回默认值 0`);
                return 0;
            }

            getNestedValue(obj, path) {
                return path.split('.').reduce((current, key) => current?.[key], obj);
            }

            getLatestMonthlyVisits(data) {
                const monthlyVisits = data.EstimatedMonthlyVisits;
                if (!monthlyVisits || typeof monthlyVisits !== 'object') {
                    return 0;
                }

                // 获取所有日期键并排序，取最新的
                const dates = Object.keys(monthlyVisits).sort().reverse();
                if (dates.length === 0) {
                    return 0;
                }

                const latestDate = dates[0];
                const visits = monthlyVisits[latestDate];
                console.log(`从 EstimatedMonthlyVisits 获取最新访问量: ${latestDate} = ${visits}`);

                return typeof visits === 'number' ? visits : 0;
            }

            getCountryNameFromNumericCode(code) {
                const countryMap = {
                    392: '日本',
                    410: '韩国',
                    156: '中国',
                    840: '美国',
                    826: '英国'
                };
                return countryMap[code] || `国家${code}`;
            }

            getCountryCodeFromNumericCode(code) {
                const codeMap = {
                    392: 'JP',
                    410: 'KR', 
                    156: 'CN',
                    840: 'US',
                    826: 'GB'
                };
                return codeMap[code] || code.toString();
            }

            transformCountriesData(countries) {
                if (!Array.isArray(countries)) return [];

                console.log('原始国家数据:', countries);

                return countries.slice(0, 5).map((country) => {
                    // 提取国家代码和名称 - 适配新的API结构
                    let countryCode = country.CountryCode || country.code || country.country_code || country.Code || '';
                    let countryName = country.name || country.country_name || country.Name || '';

                    // 处理 Country 字段（可能是数字或字符串）
                    const countryField = country.Country;
                    if (countryField !== undefined) {
                        if (typeof countryField === 'number') {
                            // Country 是数字代码，转换为国家名称
                            countryName = this.getCountryNameFromNumericCode(countryField);
                            if (!countryCode) {
                                countryCode = this.getCountryCodeFromNumericCode(countryField);
                            }
                        } else if (typeof countryField === 'string') {
                            // Country 是字符串，可能是国家名称
                            if (!countryName) {
                                countryName = countryField;
                            }
                        }
                    }

                    // 如果countryCode是数字，尝试转换为国家名称
                    if (typeof countryCode === 'number' || (typeof countryCode === 'string' && /^\d+$/.test(countryCode))) {
                        const numericCode = parseInt(countryCode.toString());
                        if (!countryName) {
                            countryName = this.getCountryNameFromNumericCode(numericCode);
                        }
                        countryCode = this.getCountryCodeFromNumericCode(numericCode);
                    }

                    const trafficShare = this.extractNumber(country, ['Value', 'share', 'traffic_share', 'Share']);
                    const visits = this.extractNumber(country, ['visits', 'Visits']);

                    console.log(`国家数据转换: 原始=${JSON.stringify(country)} -> 代码=${countryCode}, 名称=${countryName}, 份额=${trafficShare}, 访问=${visits}`);

                    return {
                        countryCode,
                        countryName,
                        trafficShare: trafficShare, // trafficShare已经是小数形式（如0.8表示80%）
                        visits
                    };
                }).filter(country => country.countryCode || country.countryName);
            }

            transformApiResponse(domain, apiData) {
                console.log('开始转换API响应数据:', JSON.stringify(apiData, null, 2));
                
                let data = apiData;
                if (Array.isArray(apiData) && apiData.length > 0) {
                    data = apiData[0];
                }
                
                // 支持多种可能的字段名和数据结构
                const traffic = {
                    domain,
                    totalVisits: this.extractNumber(data, [
                        'Engagments.Visits', 'Visits', 'visits', 'totalVisits', 'EstimatedMonthlyVisits',
                        'EstimateCounts.visits', 'Estimations.visits'
                    ]) || this.getLatestMonthlyVisits(data),
                    uniqueVisitors: this.extractNumber(data, [
                        'unique_visitors', 'uniqueVisitors', 'UniqueVisitors',
                        'EstimateCounts.unique_visitors', 'Estimations.unique_visitors'
                    ]),
                    pageViews: this.extractNumber(data, [
                        'page_views', 'pageViews', 'PageViews',
                        'EstimateCounts.page_views', 'Estimations.page_views'
                    ]),
                    bounceRate: this.extractNumber(data, [
                        'Engagments.BounceRate', 'BounceRate', 'bounce_rate', 'bounceRate',
                        'EstimateCounts.bounce_rate', 'Estimations.bounce_rate'
                    ]), // BounceRate已经是小数形式，不需要除以100
                    averageVisitDuration: this.extractNumber(data, [
                        'Engagments.TimeOnSite', 'avg_visit_duration', 'averageVisitDuration', 'AvgVisitDuration',
                        'EstimateCounts.avg_visit_duration', 'Estimations.avg_visit_duration'
                    ]),
                    pagesPerVisit: this.extractNumber(data, [
                        'Engagments.PagePerVisit', 'PagePerVisit', 'pages_per_visit', 'pagesPerVisit', 'PagesPerVisit',
                        'EstimateCounts.pages_per_visit', 'Estimations.pages_per_visit'
                    ]),
                    lastUpdated: new Date()
                };

                console.log('转换后的流量数据:', traffic);

                const topCountries = this.transformCountriesData(
                    data.TopCountryShares || data.countries || data.Countries ||
                    data.CountryBreakdown || data.country_breakdown || []
                );

                const result = {
                    domain,
                    traffic,
                    topCountries,
                    topKeywords: [],
                    globalRank: this.extractNumber(data, [
                        'global_rank', 'globalRank', 'GlobalRank', 'Rank.Global'
                    ])
                };

                console.log('最终转换结果:', result);
                return result;
            }
        }

        function runTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>🔄 测试进行中...</h2>';

            try {
                const service = new TestSimilarWebService();
                const transformedData = service.transformApiResponse('aimcp.info', mockApiResponse);

                let html = '<div class="test-section"><h2>✅ 测试结果</h2>';
                
                // 验证关键指标
                const checks = [
                    {
                        name: '总访问量',
                        expected: 360,
                        actual: transformedData.traffic.totalVisits,
                        field: 'totalVisits'
                    },
                    {
                        name: '跳出率',
                        expected: 0.442,
                        actual: transformedData.traffic.bounceRate,
                        field: 'bounceRate',
                        tolerance: 0.001
                    },
                    {
                        name: '页面/访问',
                        expected: 1.022,
                        actual: transformedData.traffic.pagesPerVisit,
                        field: 'pagesPerVisit',
                        tolerance: 0.001
                    }
                ];

                checks.forEach(check => {
                    const isValid = check.tolerance ? 
                        Math.abs(check.actual - check.expected) < check.tolerance :
                        check.actual === check.expected;
                    
                    const status = isValid ? '✅' : '❌';
                    const className = isValid ? 'success' : 'error';
                    
                    html += `<div class="result ${className}">
                        ${status} <strong>${check.name}</strong>: ${check.actual} (期望: ${check.expected})
                    </div>`;
                });

                // 检查国家数据
                html += '<h3>🌍 国家数据转换</h3>';
                transformedData.topCountries.forEach((country, index) => {
                    const isValidName = !(/^\d+$/.test(country.countryName));
                    const status = isValidName ? '✅' : '❌';
                    const className = isValidName ? 'success' : 'error';
                    
                    html += `<div class="result ${className}">
                        ${status} #${index + 1}: ${country.countryName} (${country.countryCode}) - ${(country.trafficShare * 100).toFixed(1)}%
                    </div>`;
                });

                // 显示完整转换结果
                html += '<h3>📊 完整转换结果</h3>';
                html += `<pre>${JSON.stringify(transformedData, null, 2)}</pre>`;
                
                html += '</div>';
                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-section">
                        <h2>❌ 测试失败</h2>
                        <div class="result error">
                            <strong>错误:</strong> ${error.message}
                        </div>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
