# 无限循环问题修复说明

## 问题描述
在重构页面扫描器集成表单检测功能后，插件出现了一直处于刷新状态、无法点击任何按键的问题。

## 问题根因
代码中存在多个无限循环，主要原因是：

### 1. useFormDetection.ts 中的依赖循环
```typescript
// 问题代码
const detectForms = useCallback(async () => {
  // ...
}, [storeDetectForms, detectedForms, onFormDetected, onError])
//                    ^^^^^^^^^^^^^ 这里导致无限循环
```

当 `detectForms` 被调用时：
1. `storeDetectForms` 更新 `detectedForms` 状态
2. `detectedForms` 变化导致 `detectForms` 函数重新创建
3. `useEffect` 监听 `detectForms` 变化，又调用 `detectForms`
4. 无限循环

### 2. useAutoFormDetection.ts 中的依赖循环
```typescript
// 问题代码
const debouncedDetection = useCallback((reason: string) => {
  // ...
}, [enabled, debounceMs, onAutoDetectionTriggered, formDetection])
//                                                  ^^^^^^^^^^^^^ 这里导致无限循环
```

`formDetection` 对象每次都会重新创建，导致 `debouncedDetection` 函数不断重新创建。

## 修复方案

### 1. 移除有问题的依赖
- 从 `detectForms` 的依赖数组中移除 `detectedForms`
- 从 `debouncedDetection` 的依赖数组中移除 `formDetection`
- 从 `useEffect` 的依赖数组中移除会变化的函数引用

### 2. 使用 useRef 存储函数引用
```typescript
// 修复后的代码
const detectFormsRef = useRef(detectForms)
detectFormsRef.current = detectForms

// 在需要调用的地方使用 ref
detectFormsRef.current()
```

### 3. 分离回调处理
将 `onFormDetected` 回调处理移到单独的 `useEffect` 中：
```typescript
// 处理 onFormDetected 回调
useEffect(() => {
  if (onFormDetected && detectedForms.length > 0) {
    onFormDetected(detectedForms)
  }
}, [detectedForms, onFormDetected])
```

### 4. 使用稳定的回调函数
```typescript
// 在组件中使用 useCallback 创建稳定的回调
const onAutoDetectionTriggered = useCallback((reason: string) => {
  console.log(`页面扫描器触发自动表单检测: ${reason}`)
}, [])
```

## 修复后的文件
- `extension/src/hooks/useFormDetection.ts`
- `extension/src/hooks/useAutoFormDetection.ts`
- `extension/src/components/scanner/page-scanner.tsx`

## 验证修复
1. 构建扩展：`npm run build`
2. 加载扩展到浏览器
3. 打开扩展侧边栏
4. 切换到"探索模式"
5. 验证页面扫描器不再一直刷新
6. 验证按钮可以正常点击
7. 验证表单检测功能正常工作

## 预防措施
1. 在使用 `useCallback` 时，仔细检查依赖数组
2. 避免在依赖数组中包含会频繁变化的对象或数组
3. 使用 `useRef` 来存储需要在 `useEffect` 中使用但不应该作为依赖的函数
4. 分离副作用和状态更新逻辑
5. 使用 ESLint 的 `exhaustive-deps` 规则，但要理解何时可以安全地忽略警告

## 测试建议
使用提供的测试页面 `test-page-scanner-integration.html` 来验证：
- 自动表单检测功能
- 页面导航时的自动检测
- 动态内容变化检测
- 手动检测按钮功能
