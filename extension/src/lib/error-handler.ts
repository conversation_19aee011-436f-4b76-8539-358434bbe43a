// 错误处理和用户反馈系统

export interface AppError {
  id: string
  type: 'network' | 'api' | 'validation' | 'storage' | 'form' | 'ai' | 'form-detection' | 'unknown'
  message: string
  details?: string
  timestamp: Date
  isRetryable: boolean
  retryCount?: number
}

export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private errorListeners: Array<(error: AppError) => void> = []
  private toastListeners: Array<(toast: Toast) => void> = []
  private errors: Map<string, AppError> = new Map()

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  // 注册错误监听器
  onError(listener: (error: AppError) => void): () => void {
    this.errorListeners.push(listener)
    return () => {
      const index = this.errorListeners.indexOf(listener)
      if (index > -1) {
        this.errorListeners.splice(index, 1)
      }
    }
  }

  // 注册Toast监听器
  onToast(listener: (toast: Toast) => void): () => void {
    this.toastListeners.push(listener)
    return () => {
      const index = this.toastListeners.indexOf(listener)
      if (index > -1) {
        this.toastListeners.splice(index, 1)
      }
    }
  }

  // 处理错误
  handleError(error: any, context?: string): AppError {
    const appError = this.createAppError(error, context)
    
    // 保存错误
    this.errors.set(appError.id, appError)
    
    // 通知监听器
    this.errorListeners.forEach(listener => {
      try {
        listener(appError)
      } catch (e) {
        console.error('Error listener failed:', e)
      }
    })

    // 记录到控制台
    console.error(`[${appError.type}] ${appError.message}`, {
      id: appError.id,
      details: appError.details,
      context,
      originalError: error
    })

    return appError
  }

  // 创建应用错误对象
  private createAppError(error: any, context?: string): AppError {
    const id = this.generateErrorId()
    const timestamp = new Date()

    // 分析错误类型
    const type = this.analyzeErrorType(error, context)
    
    // 生成用户友好的错误消息
    const message = this.generateUserMessage(error, type, context)
    
    // 生成详细信息
    const details = this.generateErrorDetails(error)

    // 判断是否可重试
    const isRetryable = this.isRetryableError(error, type)

    return {
      id,
      type,
      message,
      details,
      timestamp,
      isRetryable
    }
  }

  // 分析错误类型
  private analyzeErrorType(error: any, context?: string): AppError['type'] {
    if (!navigator.onLine) {
      return 'network'
    }

    if (error?.name === 'AbortError' || error?.message?.includes('timeout')) {
      return 'network'
    }

    if (error?.message?.includes('API') || error?.status) {
      return 'api'
    }

    if (context?.includes('form') || error?.message?.includes('validation')) {
      return 'validation'
    }

    if (context?.includes('storage') || error?.message?.includes('storage')) {
      return 'storage'
    }

    if (context?.includes('ai') || error?.message?.includes('optimize')) {
      return 'ai'
    }

    if (error?.message?.includes('form')) {
      return 'form'
    }

    return 'unknown'
  }

  // 生成用户友好的错误消息
  private generateUserMessage(error: any, type: AppError['type'], context?: string): string {
    switch (type) {
      case 'network':
        if (!navigator.onLine) {
          return '网络连接已断开，请检查网络设置'
        }
        return '网络请求失败，请稍后重试'

      case 'api':
        if (error?.status === 401) {
          return 'API密钥无效，请检查配置'
        }
        if (error?.status === 403) {
          return '权限不足，请联系管理员'
        }
        if (error?.status === 429) {
          return '请求过于频繁，请稍后重试'
        }
        if (error?.status >= 500) {
          return '服务器暂时不可用，请稍后重试'
        }
        return 'API请求失败，请稍后重试'

      case 'validation':
        return '输入信息有误，请检查后重试'

      case 'storage':
        return '数据保存失败，请稍后重试'

      case 'form':
        return '表单处理失败，请检查页面表单'

      case 'ai':
        return 'AI优化服务暂时不可用，请稍后重试'

      case 'form-detection':
        return '表单检测暂时不可用，请稍后重试'

      default:
        return error?.message || '操作失败，请稍后重试'
    }
  }

  // 生成错误详细信息
  private generateErrorDetails(error: any): string | undefined {
    if (error?.stack) {
      return error.stack
    }
    if (error?.response?.data) {
      return JSON.stringify(error.response.data)
    }
    if (typeof error === 'object') {
      return JSON.stringify(error)
    }
    return undefined
  }

  // 判断错误是否可重试
  private isRetryableError(error: any, type: AppError['type']): boolean {
    // 网络错误通常可重试
    if (type === 'network') {
      return true
    }

    // API错误中的特定状态码可重试
    if (type === 'api') {
      const status = error?.status
      return status >= 500 || status === 429 || status === 408
    }

    // 存储错误可重试
    if (type === 'storage') {
      return true
    }

    // AI服务错误可重试
    if (type === 'ai') {
      return true
    }

    return false
  }

  // 生成错误ID
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 显示Toast通知
  showToast(toast: Omit<Toast, 'id'>): string {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const fullToast: Toast = { ...toast, id }

    this.toastListeners.forEach(listener => {
      try {
        listener(fullToast)
      } catch (e) {
        console.error('Toast listener failed:', e)
      }
    })

    return id
  }

  // 显示成功消息
  showSuccess(title: string, message?: string, duration?: number): string {
    return this.showToast({
      type: 'success',
      title,
      message,
      duration: duration || 3000
    })
  }

  // 显示错误消息
  showError(title: string, message?: string, actions?: Toast['actions']): string {
    return this.showToast({
      type: 'error',
      title,
      message,
      duration: 5000,
      actions
    })
  }

  // 显示警告消息
  showWarning(title: string, message?: string): string {
    return this.showToast({
      type: 'warning',
      title,
      message,
      duration: 4000
    })
  }

  // 显示信息消息
  showInfo(title: string, message?: string): string {
    return this.showToast({
      type: 'info',
      title,
      message,
      duration: 3000
    })
  }

  // 重试错误
  async retryError(errorId: string, retryFn: () => Promise<any>): Promise<boolean> {
    const error = this.errors.get(errorId)
    if (!error || !error.isRetryable) {
      return false
    }

    try {
      await retryFn()
      this.clearError(errorId)
      this.showSuccess('操作成功', '重试成功完成')
      return true
    } catch (retryError) {
      const updatedError = {
        ...error,
        retryCount: (error.retryCount || 0) + 1,
        timestamp: new Date()
      }
      this.errors.set(errorId, updatedError)
      this.handleError(retryError, 'retry')
      return false
    }
  }

  // 清除错误
  clearError(errorId: string): void {
    this.errors.delete(errorId)
  }

  // 清除所有错误
  clearAllErrors(): void {
    this.errors.clear()
  }

  // 获取错误列表
  getErrors(): AppError[] {
    return Array.from(this.errors.values()).sort((a, b) => 
      b.timestamp.getTime() - a.timestamp.getTime()
    )
  }

  // 获取错误统计
  getErrorStats(): {
    total: number
    byType: Record<AppError['type'], number>
    retryable: number
  } {
    const errors = this.getErrors()
    const byType = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<AppError['type'], number>)

    return {
      total: errors.length,
      byType,
      retryable: errors.filter(e => e.isRetryable).length
    }
  }
}

// 全局错误处理器实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷方法
export const handleError = (error: any, context?: string) => 
  errorHandler.handleError(error, context)

export const showToast = {
  success: (title: string, message?: string) => 
    errorHandler.showSuccess(title, message),
  error: (title: string, message?: string, actions?: Toast['actions']) => 
    errorHandler.showError(title, message, actions),
  warning: (title: string, message?: string) => 
    errorHandler.showWarning(title, message),
  info: (title: string, message?: string) => 
    errorHandler.showInfo(title, message)
}