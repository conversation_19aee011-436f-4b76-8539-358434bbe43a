import { useEffect, useState, useCallback, useRef } from 'react'
import { useAppStore } from '~/store'

interface UseFormDetectionOptions {
  autoDetectOnMount?: boolean
  autoDetectOnPageChange?: boolean
  onFormDetected?: (forms: any[]) => void
  onError?: (error: string) => void
}

interface FormDetectionState {
  detectedForms: any[]
  selectedForm: any | null
  isLoading: boolean
  error: string | null
  highlightedFormIndex: number | null
}

interface FormDetectionActions {
  detectForms: () => Promise<void>
  selectForm: (form: any) => void
  highlightForm: (formIndex: number) => Promise<void>
  clearHighlights: () => Promise<void>
  setHighlightedFormIndex: (index: number | null) => void
}

export function useFormDetection(options: UseFormDetectionOptions = {}): FormDetectionState & FormDetectionActions {
  const {
    autoDetectOnMount = false,
    autoDetectOnPageChange = true,
    onFormDetected,
    onError
  } = options

  const { 
    detectedForms, 
    selectedForm, 
    selectForm: storeSelectForm, 
    detectForms: storeDetectForms, 
    formDetectionLoading,
    error: storeError 
  } = useAppStore()

  const [highlightedFormIndex, setHighlightedFormIndex] = useState<number | null>(null)

  // Handle form detection
  const detectForms = useCallback(async () => {
    try {
      await storeDetectForms()
      // Note: We'll handle onFormDetected in a separate useEffect to avoid dependency issues
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '表单检测失败'
      if (onError) {
        onError(errorMessage)
      }
    }
  }, [storeDetectForms, onError])

  // Handle form selection
  const selectForm = useCallback((form: any) => {
    storeSelectForm(form)
  }, [storeSelectForm])

  // Handle form highlighting
  const highlightForm = useCallback(async (formIndex: number) => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await chrome.tabs.sendMessage(tab.id, {
        type: 'HIGHLIGHT_FORM',
        formIndex
      })
      
      setHighlightedFormIndex(formIndex)
    } catch (error) {
      console.error('高亮表单失败:', error)
      if (onError) {
        onError('高亮表单失败')
      }
    }
  }, [onError])

  // Handle clearing highlights
  const clearHighlights = useCallback(async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await chrome.tabs.sendMessage(tab.id, {
        type: 'CLEAR_HIGHLIGHTS'
      })
      
      setHighlightedFormIndex(null)
    } catch (error) {
      console.error('清除高亮失败:', error)
      if (onError) {
        onError('清除高亮失败')
      }
    }
  }, [onError])

  // Handle onFormDetected callback when forms change
  useEffect(() => {
    if (onFormDetected && detectedForms.length > 0) {
      onFormDetected(detectedForms)
    }
  }, [detectedForms, onFormDetected])

  // Store detectForms function reference to avoid dependency issues
  const detectFormsRef = useRef(detectForms)
  detectFormsRef.current = detectForms

  // Listen for page structure changes
  useEffect(() => {
    if (!autoDetectOnPageChange) return

    const handleMessage = (message: any) => {
      if (message.type === 'PAGE_STRUCTURE_CHANGED') {
        console.log('页面结构发生变化，自动重新检测表单')
        detectFormsRef.current()
      }
    }

    chrome.runtime.onMessage.addListener(handleMessage)
    return () => chrome.runtime.onMessage.removeListener(handleMessage)
  }, [autoDetectOnPageChange])

  // Auto-detect on mount
  useEffect(() => {
    if (autoDetectOnMount) {
      detectFormsRef.current()
    }
  }, [autoDetectOnMount])

  return {
    // State
    detectedForms,
    selectedForm,
    isLoading: formDetectionLoading,
    error: storeError,
    highlightedFormIndex,
    
    // Actions
    detectForms,
    selectForm,
    highlightForm,
    clearHighlights,
    setHighlightedFormIndex
  }
}
