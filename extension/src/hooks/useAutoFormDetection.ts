import { useEffect, useRef, useCallback } from 'react'
import { useFormDetection } from './useFormDetection'

interface UseAutoFormDetectionOptions {
  enabled?: boolean
  detectOnTabActivation?: boolean
  detectOnUrlChange?: boolean
  detectOnPageLoad?: boolean
  detectOnPageStructureChange?: boolean
  debounceMs?: number
  onAutoDetectionTriggered?: (reason: string) => void
}

export function useAutoFormDetection(options: UseAutoFormDetectionOptions = {}) {
  const {
    enabled = true,
    detectOnTabActivation = true,
    detectOnUrlChange = true,
    detectOnPageLoad = true,
    detectOnPageStructureChange = true,
    debounceMs = 1000,
    onAutoDetectionTriggered
  } = options

  const currentUrlRef = useRef<string>('')
  const debounceTimerRef = useRef<NodeJS.Timeout>()

  const formDetection = useFormDetection({
    autoDetectOnMount: detectOnPageLoad,
    autoDetectOnPageChange: detectOnPageStructureChange,
    onFormDetected: (forms) => {
      console.log(`自动检测到 ${forms.length} 个表单`)
    },
    onError: (error) => {
      console.error('自动表单检测失败:', error)
    }
  })

  // Store detectForms function reference to avoid dependency issues
  const detectFormsRef = useRef(formDetection.detectForms)
  detectFormsRef.current = formDetection.detectForms

  // Debounced detection function
  const debouncedDetection = useCallback((reason: string) => {
    if (!enabled) return

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(() => {
      console.log(`触发自动表单检测: ${reason}`)
      if (onAutoDetectionTriggered) {
        onAutoDetectionTriggered(reason)
      }
      detectFormsRef.current()
    }, debounceMs)
  }, [enabled, debounceMs, onAutoDetectionTriggered]) // Removed formDetection from dependencies

  // Monitor tab activation
  useEffect(() => {
    if (!enabled || !detectOnTabActivation) return

    const handleTabActivated = async (activeInfo: chrome.tabs.TabActiveInfo) => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tab?.url && tab.url !== currentUrlRef.current) {
          currentUrlRef.current = tab.url
          debouncedDetection('标签页激活')
        }
      } catch (error) {
        console.error('处理标签页激活事件失败:', error)
      }
    }

    if (chrome?.tabs?.onActivated) {
      chrome.tabs.onActivated.addListener(handleTabActivated)
      return () => chrome.tabs.onActivated.removeListener(handleTabActivated)
    }
  }, [enabled, detectOnTabActivation]) // Removed debouncedDetection from dependencies

  // Monitor URL changes
  useEffect(() => {
    if (!enabled || !detectOnUrlChange) return

    const handleTabUpdated = async (
      tabId: number,
      changeInfo: chrome.tabs.TabChangeInfo,
      tab: chrome.tabs.Tab
    ) => {
      // Only trigger on URL changes for the active tab
      if (changeInfo.url && tab.active) {
        const newUrl = changeInfo.url
        if (newUrl !== currentUrlRef.current) {
          currentUrlRef.current = newUrl
          debouncedDetection('URL变化')
        }
      }
    }

    if (chrome?.tabs?.onUpdated) {
      chrome.tabs.onUpdated.addListener(handleTabUpdated)
      return () => chrome.tabs.onUpdated.removeListener(handleTabUpdated)
    }
  }, [enabled, detectOnUrlChange]) // Removed debouncedDetection from dependencies

  // Initialize current URL
  useEffect(() => {
    const initializeCurrentUrl = async () => {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tab?.url) {
          currentUrlRef.current = tab.url
        }
      } catch (error) {
        console.error('初始化当前URL失败:', error)
      }
    }

    if (enabled) {
      initializeCurrentUrl()
    }
  }, [enabled])

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return {
    ...formDetection,
    isAutoDetectionEnabled: enabled,
    currentUrl: currentUrlRef.current,
    triggerManualDetection: () => debouncedDetection('手动触发')
  }
}
