import React, { useEffect } from 'react'
import { useAppStore } from '~/store'
import { ToastComponent } from './toast'

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const { toasts, removeToast } = useAppStore()

  return (
    <>
      {children}
      
      {/* Toast 容器 */}
      <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full pointer-events-none">
        {toasts.map(toast => (
          <div key={toast.id} className="pointer-events-auto">
            <ToastComponent
              toast={toast}
              onClose={removeToast}
            />
          </div>
        ))}
      </div>
    </>
  )
}

// Hook to use toast notifications
export function useToast() {
  const { addToast } = useAppStore()

  return {
    toast: {
      success: (title: string, message?: string) => 
        addToast({ type: 'success', title, message, duration: 3000 }),
      error: (title: string, message?: string) => 
        addToast({ type: 'error', title, message, duration: 5000 }),
      warning: (title: string, message?: string) => 
        addToast({ type: 'warning', title, message, duration: 4000 }),
      info: (title: string, message?: string) => 
        addToast({ type: 'info', title, message, duration: 3000 })
    }
  }
}
