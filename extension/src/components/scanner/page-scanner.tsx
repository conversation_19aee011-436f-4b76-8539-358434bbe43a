import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Skeleton } from '~/components/ui/skeleton'
import type { PageScanResult, SubmitLink } from '~/types'
import { submitLinkScanner } from '~/services/page-scanner'
import { useAutoFormDetection } from '~/hooks'

interface PageScannerProps {
  onLinksFound?: (newLinks: SubmitLink[]) => void
  onLinksSaved?: (savedLinks: any[]) => void
  onFormSelected?: (form: any) => void
  className?: string
}

export function PageScanner({
  onLinksFound,
  onLinksSaved,
  onFormSelected,
  className = ''
}: PageScannerProps) {
  const [scanResult, setScanResult] = useState<PageScanResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [savedLinks, setSavedLinks] = useState<Set<string>>(new Set())

  // Use automatic form detection with stable callback
  const onAutoDetectionTriggered = useCallback((reason: string) => {
    console.log(`页面扫描器触发自动表单检测: ${reason}`)
  }, [])

  const formDetection = useAutoFormDetection({
    enabled: true,
    detectOnTabActivation: true,
    detectOnUrlChange: true,
    detectOnPageLoad: true,
    detectOnPageStructureChange: true,
    onAutoDetectionTriggered
  })

  const handleScan = async () => {
    setLoading(true)
    setError(null)

    try {
      // Scan for submit links
      const result = await submitLinkScanner.scanCurrentPage()
      setScanResult(result)

      if (result && result.newLinksNotInDatabase.length > 0 && onLinksFound) {
        onLinksFound(result.newLinksNotInDatabase)
      }

      // Also trigger form detection
      await formDetection.detectForms()
    } catch (err) {
      console.error('Failed to scan page:', err)
      setError('页面扫描失败，请检查页面权限')
    } finally {
      setLoading(false)
    }
  }

  const handleSelectForm = (form: any) => {
    formDetection.selectForm(form)
    if (onFormSelected) {
      onFormSelected(form)
    }
  }

  const handleSaveLinks = async (links: SubmitLink[]) => {
    setSaving(true)
    setError(null)
    
    try {
      const saved = await submitLinkScanner.saveNewSubmitLinks(links)
      
      // 更新已保存链接的状态
      const newSavedUrls = new Set(savedLinks)
      links.forEach(link => newSavedUrls.add(link.url))
      setSavedLinks(newSavedUrls)
      
      if (onLinksSaved) {
        onLinksSaved(saved)
      }
      
      // 更新扫描结果，移除已保存的链接
      if (scanResult) {
        setScanResult({
          ...scanResult,
          newLinksNotInDatabase: scanResult.newLinksNotInDatabase.filter(
            link => !newSavedUrls.has(link.url)
          )
        })
      }
    } catch (err) {
      console.error('Failed to save links:', err)
      setError('保存链接失败，请稍后重试')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveSingleLink = (link: SubmitLink) => {
    handleSaveLinks([link])
  }

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  const getFormConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getLinkTypeText = (type: SubmitLink['type']): string => {
    const typeMap: Record<SubmitLink['type'], string> = {
      'submit': '提交',
      'form': '表单',
      'signup': '注册',
      'contact': '联系',
      'apply': '申请'
    }
    return typeMap[type] || type
  }

  return (
    <Card className={className}>
      <CardContent className="py-4">
        <div className="space-y-4">
          {/* 标题栏 */}
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm">页面扫描</h3>
            <div className="flex gap-2">
              <Button
                onClick={formDetection.detectForms}
                disabled={formDetection.isLoading}
                size="sm"
                variant="outline"
                className="h-8"
              >
                {formDetection.isLoading ? (
                  <>
                    <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                    检测表单...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    检测表单
                  </>
                )}
              </Button>
              <Button
                onClick={handleScan}
                disabled={loading || formDetection.isLoading}
                size="sm"
                className="h-8"
              >
                {loading ? (
                  <>
                    <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                    扫描中...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    全部扫描
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 错误提示 */}
          {(error || formDetection.error) && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded space-y-1">
              {error && <div>{error}</div>}
              {formDetection.error && <div>表单检测: {formDetection.error}</div>}
            </div>
          )}

          {/* 加载状态 */}
          {(loading || formDetection.isLoading) && (
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              {formDetection.isLoading && (
                <div className="text-xs text-muted-foreground">
                  正在检测表单...
                </div>
              )}
            </div>
          )}

          {/* 扫描结果 */}
          {(scanResult || formDetection.detectedForms.length > 0) && !loading && !formDetection.isLoading && (
            <div className="space-y-4">
              {/* 统计信息 */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                {scanResult && (
                  <>
                    <div className="space-y-1">
                      <div className="text-muted-foreground">发现链接</div>
                      <div className="font-semibold">{scanResult.totalLinksFound}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-muted-foreground">新链接</div>
                      <div className="font-semibold text-green-600">
                        {scanResult.newLinksNotInDatabase.length}
                      </div>
                    </div>
                  </>
                )}
                <div className="space-y-1">
                  <div className="text-muted-foreground">检测表单</div>
                  <div className="font-semibold text-blue-600">
                    {formDetection.detectedForms.length}
                  </div>
                </div>
              </div>

              {/* 新发现的链接 */}
              {scanResult && scanResult.newLinksNotInDatabase.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">新发现的提交链接</h4>
                    <Button
                      onClick={() => handleSaveLinks(scanResult.newLinksNotInDatabase)}
                      disabled={saving}
                      size="sm"
                      variant="outline"
                      className="h-7 text-xs"
                    >
                      {saving ? '保存中...' : '全部保存'}
                    </Button>
                  </div>
                  
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {scanResult.newLinksNotInDatabase.map((link, index) => (
                      <div 
                        key={`${link.url}-${index}`} 
                        className="border rounded p-3 space-y-2"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                              {link.text || '无标题'}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {link.url}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2 ml-2">
                            <span className={`text-xs font-medium ${getConfidenceColor(link.confidence)}`}>
                              {getConfidenceText(link.confidence)}
                            </span>
                            <Button
                              onClick={() => handleSaveSingleLink(link)}
                              disabled={saving || savedLinks.has(link.url)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                            >
                              {savedLinks.has(link.url) ? (
                                <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                              )}
                            </Button>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 flex-wrap">
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded">
                            {getLinkTypeText(link.type)}
                          </span>
                          <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded">
                            {link.isInternalLink ? '内部链接' : '外部链接'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            置信度: {Math.round(link.confidence * 100)}%
                          </span>
                        </div>
                        
                        {link.context && (
                          <div className="text-xs text-muted-foreground">
                            上下文: {link.context}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 检测到的表单 */}
              {formDetection.detectedForms.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">检测到的表单</h4>
                    <span className="text-xs text-muted-foreground">
                      {formDetection.detectedForms.length} 个表单
                    </span>
                  </div>

                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {formDetection.detectedForms.map((form, index) => (
                      <Card
                        key={index}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          formDetection.selectedForm?.index === index ? 'ring-2 ring-primary ring-offset-2' : ''
                        } ${
                          formDetection.highlightedFormIndex === index ? 'ring-2 ring-blue-500 ring-offset-2' : ''
                        }`}
                        onClick={() => handleSelectForm(form)}
                      >
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <CardTitle className="text-sm flex items-center gap-2">
                                表单 #{index + 1}
                                {form.context?.title && (
                                  <span className="text-xs font-normal text-muted-foreground">
                                    - {form.context.title}
                                  </span>
                                )}
                              </CardTitle>
                              <div className="flex items-center gap-2 mt-1">
                                <span className={`text-xs font-medium ${getFormConfidenceColor(form.confidence)}`}>
                                  置信度: {Math.round(form.confidence * 100)}%
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {form.fields.length} 个字段
                                </span>
                              </div>
                            </div>

                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  formDetection.highlightForm(index)
                                }}
                                className={`h-6 px-2 text-xs ${
                                  formDetection.highlightedFormIndex === index ? 'bg-blue-100 text-blue-600' : ''
                                }`}
                              >
                                {formDetection.highlightedFormIndex === index ? '已高亮' : '高亮'}
                              </Button>
                            </div>
                          </div>

                          {form.context?.action && (
                            <div className="mt-1 text-xs text-muted-foreground">
                              提交到: {form.context.action}
                            </div>
                          )}
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* 所有链接摘要 */}
              {scanResult && scanResult.submitLinks.length > scanResult.newLinksNotInDatabase.length && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">
                    已存在的链接 ({scanResult.submitLinks.length - scanResult.newLinksNotInDatabase.length})
                  </h4>
                  <div className="text-xs text-muted-foreground">
                    这些链接已在您的外链库中，无需重复添加
                  </div>
                </div>
              )}

              {/* 扫描时间 */}
              <div className="text-xs text-muted-foreground space-y-1">
                {scanResult && (
                  <div>链接扫描时间: {scanResult.scannedAt.toLocaleString()}</div>
                )}
                {formDetection.detectedForms.length > 0 && (
                  <div>表单检测: 自动检测已启用</div>
                )}
              </div>
            </div>
          )}

          {/* 空状态 */}
          {!scanResult && formDetection.detectedForms.length === 0 && !loading && !formDetection.isLoading && !error && !formDetection.error && (
            <div className="text-center text-muted-foreground text-sm py-8">
              <div className="space-y-2">
                <div>点击"全部扫描"开始查找提交相关的链接和表单</div>
                <div className="text-xs">或点击"检测表单"仅检测页面表单</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default PageScanner