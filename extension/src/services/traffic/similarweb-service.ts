import type { SimilarWebData, WebsiteTraffic, TrafficCountry, TrafficKeyword } from '~/types'

export class SimilarWebService {
  private readonly baseUrl = 'https://data.similarweb.com/api/v1/data'
  
  constructor() {}

  /**
   * 获取网站流量数据
   */
  async getWebsiteTraffic(domain: string): Promise<SimilarWebData | null> {
    try {
      const cleanDomain = this.cleanDomain(domain)
      console.log(`正在获取域名 ${cleanDomain} 的流量数据...`)
      
      const response = await fetch(`${this.baseUrl}?domain=${cleanDomain}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'LinkTrackPro-Extension/1.0'
        }
      })
      
      if (!response.ok) {
        console.warn(`API请求失败 ${domain}:`, {
          status: response.status,
          statusText: response.statusText,
          url: response.url
        })
        return null
      }
      
      const responseText = await response.text()
      console.log('API原始响应:', responseText)
      
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('JSON解析失败:', parseError, '原始响应:', responseText)
        return null
      }
      
      const transformedData = this.transformApiResponse(cleanDomain, data)
      console.log('转换后的数据:', transformedData)
      
      return transformedData
    } catch (error) {
      console.error('获取SimilarWeb数据时出错:', error)
      return null
    }
  }

  /**
   * 获取当前页面的流量数据
   */
  async getCurrentPageTraffic(): Promise<SimilarWebData | null> {
    try {
      // 检查Chrome API是否可用
      if (!chrome?.tabs) {
        console.warn('Chrome tabs API not available, using fallback')
        return this.getCurrentPageTrafficFallback()
      }

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab?.url) {
        console.warn('无法获取当前页面URL，使用备选方案')
        return this.getCurrentPageTrafficFallback()
      }
      
      const domain = this.extractDomain(tab.url)
      return this.getWebsiteTraffic(domain)
    } catch (error) {
      console.error('Error getting current page traffic:', error)
      return this.getCurrentPageTrafficFallback()
    }
  }

  /**
   * 获取当前页面流量数据的备选方案
   */
  private async getCurrentPageTrafficFallback(): Promise<SimilarWebData | null> {
    try {
      // 尝试从window.location获取域名
      if (typeof window !== 'undefined' && window.location) {
        const domain = this.extractDomain(window.location.href)
        return this.getWebsiteTraffic(domain)
      }
      
      console.warn('无法获取当前域名')
      return null
    } catch (error) {
      console.error('Fallback method failed:', error)
      return null
    }
  }

  /**
   * 转换API响应数据为内部数据格式
   */
  private transformApiResponse(domain: string, apiData: any): SimilarWebData {
    console.log('开始转换API响应数据:', JSON.stringify(apiData, null, 2))
    
    // 如果API返回了数组，尝试获取第一个元素
    let data = apiData
    if (Array.isArray(apiData) && apiData.length > 0) {
      data = apiData[0]
      console.log('检测到数组响应，使用第一个元素:', data)
    }
    
    // 如果数据被包装在特定字段中，尝试提取
    if (data.result) data = data.result
    if (data.data) data = data.data
    if (data.response) data = data.response
    
    // 支持多种可能的字段名和数据结构
    const traffic: WebsiteTraffic = {
      domain,
      totalVisits: this.extractNumber(data, [
        'Engagments.Visits', 'Visits', 'visits', 'totalVisits', 'EstimatedMonthlyVisits',
        'EstimateCounts.visits', 'Estimations.visits'
      ]) || this.getLatestMonthlyVisits(data),
      uniqueVisitors: this.extractNumber(data, [
        'unique_visitors', 'uniqueVisitors', 'UniqueVisitors',
        'EstimateCounts.unique_visitors', 'Estimations.unique_visitors'
      ]),
      pageViews: this.extractNumber(data, [
        'page_views', 'pageViews', 'PageViews',
        'EstimateCounts.page_views', 'Estimations.page_views'
      ]),
      bounceRate: this.extractNumber(data, [
        'Engagments.BounceRate', 'BounceRate', 'bounce_rate', 'bounceRate',
        'EstimateCounts.bounce_rate', 'Estimations.bounce_rate'
      ]), // BounceRate已经是小数形式，不需要除以100
      averageVisitDuration: this.extractNumber(data, [
        'Engagments.TimeOnSite', 'avg_visit_duration', 'averageVisitDuration', 'AvgVisitDuration',
        'EstimateCounts.avg_visit_duration', 'Estimations.avg_visit_duration'
      ]),
      pagesPerVisit: this.extractNumber(data, [
        'Engagments.PagePerVisit', 'PagePerVisit', 'pages_per_visit', 'pagesPerVisit', 'PagesPerVisit',
        'EstimateCounts.pages_per_visit', 'Estimations.pages_per_visit'
      ]),
      lastUpdated: new Date()
    }

    console.log('转换后的流量数据:', traffic)

    const topCountries: TrafficCountry[] = this.transformCountriesData(
      data.countries || data.TopCountryShares || data.Countries || 
      data.CountryBreakdown || data.country_breakdown || []
    )
    
    const topKeywords: TrafficKeyword[] = this.transformKeywordsData(
      data.keywords || data.TopKeywords || data.Keywords || 
      data.SearchKeywords || data.search_keywords || []
    )

    console.log('转换后的国家数据:', topCountries)
    console.log('转换后的关键词数据:', topKeywords)

    const result = {
      domain,
      traffic,
      topCountries,
      topKeywords,
      globalRank: this.extractNumber(data, [
        'GlobalRank.Rank', 'global_rank', 'globalRank', 'GlobalRank', 'Rank.Global'
      ]),
      countryRank: this.extractNumber(data, [
        'CountryRank.Rank', 'country_rank', 'countryRank', 'CountryRank', 'Rank.Country'
      ]),
      categoryRank: this.extractNumber(data, [
        'CategoryRank.Rank', 'category_rank', 'categoryRank', 'CategoryRank', 'Rank.Category'
      ]),
      category: data.category || data.Category || data.MainCategory || data.main_category
    }

    console.log('最终转换结果:', result)
    return result
  }

  /**
   * 从对象中提取数字值，支持多个可能的字段名
   */
  private extractNumber(obj: any, fieldNames: string[], divisor: number = 1): number {
    for (const fieldName of fieldNames) {
      const value = this.getNestedValue(obj, fieldName)
      console.log(`提取字段 ${fieldName}:`, value, typeof value)

      if (typeof value === 'number' && !Number.isNaN(value)) {
        const result = value / divisor
        console.log(`数字值转换: ${value} / ${divisor} = ${result}`)
        return result
      }

      if (typeof value === 'string' && value.trim() !== '') {
        // 移除逗号、百分号、空格等字符
        const cleanValue = value.replace(/[,%\s]/g, '')
        const parsed = parseFloat(cleanValue)
        console.log(`字符串值转换: "${value}" -> "${cleanValue}" -> ${parsed}`)

        if (!Number.isNaN(parsed) && isFinite(parsed)) {
          const result = parsed / divisor
          console.log(`最终结果: ${parsed} / ${divisor} = ${result}`)
          return result
        }
      }
    }
    console.log(`未找到有效值，返回默认值 0`)
    return 0
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 从 EstimatedMonthlyVisits 对象中获取最新月份的访问量
   */
  private getLatestMonthlyVisits(data: any): number {
    const monthlyVisits = data.EstimatedMonthlyVisits
    if (!monthlyVisits || typeof monthlyVisits !== 'object') {
      return 0
    }

    // 获取所有日期键并排序，取最新的
    const dates = Object.keys(monthlyVisits).sort().reverse()
    if (dates.length === 0) {
      return 0
    }

    const latestDate = dates[0]
    const visits = monthlyVisits[latestDate]
    console.log(`从 EstimatedMonthlyVisits 获取最新访问量: ${latestDate} = ${visits}`)

    return typeof visits === 'number' ? visits : 0
  }

  /**
   * 转换国家流量数据
   */
  private transformCountriesData(countries: any[]): TrafficCountry[] {
    if (!Array.isArray(countries)) return []

    console.log('原始国家数据:', countries)

    return countries.slice(0, 5).map((country: any) => {
      // 提取国家代码和名称 - 适配新的API结构
      let countryCode = country.CountryCode || country.code || country.country_code || country.Code || ''
      let countryName = country.name || country.country_name || country.Name || ''

      // 处理 Country 字段（可能是数字或字符串）
      const countryField = country.Country
      if (countryField !== undefined) {
        if (typeof countryField === 'number') {
          // Country 是数字代码，转换为国家名称
          countryName = this.getCountryNameFromNumericCode(countryField)
          if (!countryCode) {
            countryCode = this.getCountryCodeFromNumericCode(countryField)
          }
        } else if (typeof countryField === 'string') {
          // Country 是字符串，可能是国家名称
          if (!countryName) {
            countryName = countryField
          }
        }
      }

      // 如果countryCode是数字，尝试转换为国家名称
      if (typeof countryCode === 'number' || (typeof countryCode === 'string' && /^\d+$/.test(countryCode))) {
        const numericCode = parseInt(countryCode.toString())
        if (!countryName) {
          countryName = this.getCountryNameFromNumericCode(numericCode)
        }
        countryCode = this.getCountryCodeFromNumericCode(numericCode)
      }

      // 如果countryName仍然是数字，尝试从其他字段获取
      if (!countryName || /^\d+$/.test(countryName)) {
        if (typeof country === 'object') {
          // 检查是否有其他可能的字段包含国家信息
          for (const key of Object.keys(country)) {
            const value = country[key]
            if (typeof value === 'string' && value.length > 2 && !/^\d+$/.test(value)) {
              countryName = value
              break
            }
          }
        }
      }

      const trafficShare = this.extractNumber(country, ['Value', 'share', 'traffic_share', 'Share'])
      const visits = this.extractNumber(country, ['visits', 'Visits'])

      console.log(`国家数据转换: 原始=${JSON.stringify(country)} -> 代码=${countryCode}, 名称=${countryName}, 份额=${trafficShare}, 访问=${visits}`)

      return {
        countryCode,
        countryName,
        trafficShare: trafficShare, // trafficShare已经是小数形式（如0.8表示80%）
        visits
      }
    }).filter(country => country.countryCode || country.countryName)
  }

  /**
   * 转换关键词数据
   */
  private transformKeywordsData(keywords: any[]): TrafficKeyword[] {
    if (!Array.isArray(keywords)) return []
    
    return keywords.slice(0, 10).map((keyword: any) => ({
      keyword: keyword.keyword || keyword.term || keyword.Keyword || keyword.Term || '',
      trafficShare: this.extractNumber(keyword, ['share', 'traffic_share', 'Share', 'Value'], 100), // 转换百分比
      position: this.extractNumber(keyword, ['position', 'Position', 'rank', 'Rank']),
      volume: this.extractNumber(keyword, ['volume', 'search_volume', 'Volume', 'SearchVolume'])
    })).filter(keyword => keyword.keyword)
  }

  /**
   * 清理域名，移除协议和www前缀
   */
  private cleanDomain(url: string): string {
    try {
      // 如果不是完整URL，直接返回
      if (!url.includes('://')) {
        return url.replace(/^www\./, '')
      }
      
      const urlObj = new URL(url)
      let hostname = urlObj.hostname
      
      // 移除www前缀
      if (hostname.startsWith('www.')) {
        hostname = hostname.substring(4)
      }
      
      return hostname
    } catch {
      // 如果URL解析失败，尝试简单处理
      return url.replace(/^(https?:\/\/)?(www\.)?/, '').split('/')[0]
    }
  }

  /**
   * 从URL中提取域名
   */
  private extractDomain(url: string): string {
    return this.cleanDomain(url)
  }

  /**
   * 根据数字国家代码获取国家名称
   */
  private getCountryNameFromNumericCode(code: number): string {
    const countryMap: { [key: number]: string } = {
      392: '日本',
      410: '韩国',
      156: '中国',
      840: '美国',
      826: '英国',
      276: '德国',
      250: '法国',
      380: '意大利',
      724: '西班牙',
      124: '加拿大',
      36: '澳大利亚',
      76: '巴西',
      356: '印度',
      643: '俄罗斯',
      528: '荷兰',
      752: '瑞典',
      578: '挪威',
      208: '丹麦',
      246: '芬兰',
      756: '瑞士',
      40: '奥地利',
      56: '比利时',
      616: '波兰',
      203: '捷克',
      348: '匈牙利',
      642: '罗马尼亚',
      300: '希腊',
      620: '葡萄牙',
      372: '爱尔兰',
      554: '新西兰',
      702: '新加坡',
      344: '香港',
      158: '台湾',
      764: '泰国',
      458: '马来西亚',
      360: '印度尼西亚',
      608: '菲律宾',
      704: '越南',
      818: '埃及',
      710: '南非',
      484: '墨西哥',
      32: '阿根廷',
      152: '智利',
      170: '哥伦比亚',
      604: '秘鲁',
      858: '乌拉圭',
      862: '委内瑞拉'
    }

    return countryMap[code] || `国家${code}`
  }

  /**
   * 根据数字国家代码获取ISO国家代码
   */
  private getCountryCodeFromNumericCode(code: number): string {
    const codeMap: { [key: number]: string } = {
      392: 'JP',
      410: 'KR',
      156: 'CN',
      840: 'US',
      826: 'GB',
      276: 'DE',
      250: 'FR',
      380: 'IT',
      724: 'ES',
      124: 'CA',
      36: 'AU',
      76: 'BR',
      356: 'IN',
      643: 'RU',
      528: 'NL',
      752: 'SE',
      578: 'NO',
      208: 'DK',
      246: 'FI',
      756: 'CH',
      40: 'AT',
      56: 'BE',
      616: 'PL',
      203: 'CZ',
      348: 'HU',
      642: 'RO',
      300: 'GR',
      620: 'PT',
      372: 'IE',
      554: 'NZ',
      702: 'SG',
      344: 'HK',
      158: 'TW',
      764: 'TH',
      458: 'MY',
      360: 'ID',
      608: 'PH',
      704: 'VN',
      818: 'EG',
      710: 'ZA',
      484: 'MX',
      32: 'AR',
      152: 'CL',
      170: 'CO',
      604: 'PE',
      858: 'UY',
      862: 'VE'
    }

    return codeMap[code] || code.toString()
  }


}

// 导出单例实例
export const similarWebService = new SimilarWebService()