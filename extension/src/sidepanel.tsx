import "~main.css"

import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'

// 导入功能组件
import { ProjectList, ProjectForm } from '~/components/project'
import { LinkList, LinkForm } from '~/components/link'
import { FormDetector, FormFiller } from '~/components/form'
import { AIOptimizer } from '~/components/ai'
import { SettingsForm } from '~/components/settings'
import { TrafficDisplay } from '~/components/traffic'
import { PageScanner } from '~/components/scanner'
import { ToastProvider } from '~/components/ui/toast-provider'

type TabType = 'growup' | 'browse'

function SidePanel() {
  const { 
    initializeApp, 
    currentView, 
    setCurrentView,
    projects,
    links,
    settings,
    error,
    clearError,
    detectForms,
    saveCurrentUrl,
    selectedForm
  } = useAppStore()

  const [currentTab, setCurrentTab] = useState<TabType>('growup')
  const [isProjectFormOpen, setIsProjectFormOpen] = useState(false)
  const [isLinkFormOpen, setIsLinkFormOpen] = useState(false)
  const [isAIOptimizerOpen, setIsAIOptimizerOpen] = useState(false)
  const [editingProject, setEditingProject] = useState(null)
  const [editingLink, setEditingLink] = useState(null)
  const [trafficRefreshTrigger, setTrafficRefreshTrigger] = useState(0)

  // 初始化应用
  useEffect(() => {
    initializeApp()
  }, [])

  // 监听浏览器标签页切换事件，自动刷新流量数据
  useEffect(() => {
    const handleTabActivated = (_activeInfo: chrome.tabs.TabActiveInfo) => {
      // 当用户切换到不同的标签页时，刷新流量数据
      if (currentTab === 'browse') {
        setTrafficRefreshTrigger(Date.now())
      }
    }

    const handleTabUpdated = (_tabId: number, changeInfo: chrome.tabs.TabChangeInfo, _tab: chrome.tabs.Tab) => {
      // 当标签页URL发生变化时，刷新流量数据
      if (changeInfo.url && currentTab === 'browse') {
        setTrafficRefreshTrigger(Date.now())
      }
    }

    // 添加事件监听器
    if (chrome?.tabs?.onActivated) {
      chrome.tabs.onActivated.addListener(handleTabActivated)
    }
    if (chrome?.tabs?.onUpdated) {
      chrome.tabs.onUpdated.addListener(handleTabUpdated)
    }

    // 清理事件监听器
    return () => {
      if (chrome?.tabs?.onActivated) {
        chrome.tabs.onActivated.removeListener(handleTabActivated)
      }
      if (chrome?.tabs?.onUpdated) {
        chrome.tabs.onUpdated.removeListener(handleTabUpdated)
      }
    }
  }, [currentTab])

  const handleQuickSaveUrl = async () => {
    try {
      await saveCurrentUrl()
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleQuickDetectForms = async () => {
    setCurrentView('forms')
    try {
      await detectForms()
    } catch (error) {
      // 错误已在store中处理
    }
  }

  const handleAddProject = () => {
    setEditingProject(null)
    setIsProjectFormOpen(true)
  }

  const handleEditProject = (project: any) => {
    setEditingProject(project)
    setIsProjectFormOpen(true)
  }

  const handleAddLink = () => {
    setEditingLink(null)
    setIsLinkFormOpen(true)
  }

  const handleEditLink = (link: any) => {
    setEditingLink(link)
    setIsLinkFormOpen(true)
  }

  const renderGrowupContent = () => (
    <div className="space-y-6">
      {/* 功能菜单 */}
      <div className="grid grid-cols-2 gap-3">
        <button 
          className="p-4 text-left rounded-lg border hover:bg-accent transition-colors"
          onClick={() => setCurrentView('projects')}
        >
          <div className="flex items-center gap-2 mb-2">
            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <div className="font-medium">项目管理</div>
          </div>
          <div className="text-sm text-muted-foreground">管理您的项目信息</div>
          <div className="text-xs text-primary mt-1">{projects.length} 个项目</div>
        </button>

        <button 
          className="p-4 text-left rounded-lg border hover:bg-accent transition-colors"
          onClick={() => setCurrentView('links')}
        >
          <div className="flex items-center gap-2 mb-2">
            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <div className="font-medium">外链库</div>
          </div>
          <div className="text-sm text-muted-foreground">维护提交平台列表</div>
          <div className="text-xs text-primary mt-1">{links.length} 个平台</div>
        </button>

        <button 
          className="p-4 text-left rounded-lg border hover:bg-accent transition-colors"
          onClick={() => setCurrentView('forms')}
        >
          <div className="flex items-center gap-2 mb-2">
            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <div className="font-medium">表单填充</div>
          </div>
          <div className="text-sm text-muted-foreground">自动识别并填充表单</div>
        </button>
      </div>

      {/* 快速操作 */}
      <div className="space-y-3">
        <h3 className="font-medium text-sm">快速操作</h3>
        <div className="space-y-2">
          <Button 
            onClick={handleQuickSaveUrl}
            variant="outline" 
            className="w-full justify-start h-auto py-3"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <div className="text-left">
              <div className="font-medium">保存当前网址</div>
              <div className="text-xs text-muted-foreground">添加到外链库</div>
            </div>
          </Button>

          <Button 
            onClick={handleQuickDetectForms}
            variant="outline" 
            className="w-full justify-start h-auto py-3"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <div className="text-left">
              <div className="font-medium">检测页面表单</div>
              <div className="text-xs text-muted-foreground">自动识别提交表单</div>
            </div>
          </Button>
        </div>
      </div>

      {/* 状态概览 */}
      <Card>
        <CardContent className="py-4">
          <h3 className="font-medium text-sm mb-3">状态概览</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">项目数量:</span>
              <span className="font-medium">{projects.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">外链平台:</span>
              <span className="font-medium">{links.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">用户类型:</span>
              <span className="font-medium">
                {settings.isPaidUser ? '付费用户' : '免费用户'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderBrowseContent = () => (
    <div className="space-y-6">
      {/* 网站流量分析 */}
      <TrafficDisplay 
        showRefreshButton={true} 
        refreshTrigger={trafficRefreshTrigger}
      />

      {/* 页面扫描 */}
      <PageScanner
        onLinksFound={(newLinks) => {
          console.log('发现新链接:', newLinks)
        }}
        onLinksSaved={(savedLinks) => {
          console.log('已保存链接:', savedLinks)
          // 刷新链接列表
          window.location.reload()
        }}
        onFormSelected={(form) => {
          console.log('页面扫描器选择表单:', form)
          // 可以选择切换到表单视图
          // setCurrentView('forms')
        }}
      />
    </div>
  )

  const renderCurrentView = () => {
    switch (currentView) {
      case 'projects':
        return (
          <ProjectList
            onAddProject={handleAddProject}
            onEditProject={handleEditProject}
          />
        )
      
      case 'links':
        return (
          <LinkList
            onAddLink={handleAddLink}
            onEditLink={handleEditLink}
          />
        )
      
      case 'forms':
        return (
          <div className="space-y-6">
            <FormDetector
              onFormSelected={() => {}}
            />
            <FormFiller
              selectedForm={selectedForm}
            />
          </div>
        )
      
      case 'settings':
        return (
          <SettingsForm
            onClose={() => setCurrentView('dashboard')}
          />
        )
      
      default: // dashboard
        return (
          <div className="space-y-6">
            {/* Tab 切换 */}
            <div className="flex bg-muted p-1 rounded-lg">
              <button
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentTab === 'growup'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => setCurrentTab('growup')}
              >
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  成长模式
                </div>
              </button>
              <button
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentTab === 'browse'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => {
                  setCurrentTab('browse')
                  // 触发流量数据刷新
                  setTrafficRefreshTrigger(Date.now())
                }}
              >
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  探索模式
                </div>
              </button>
            </div>

            {/* Tab 内容 */}
            {currentTab === 'growup' ? renderGrowupContent() : renderBrowseContent()}
          </div>
        )
    }
  }

  return (
    <ToastProvider>
      <div className="w-full h-screen bg-background text-foreground">
        <div className="flex flex-col h-full">
          {/* 标题栏 */}
          <div className="flex items-center justify-between p-4 border-b">
            {currentView === 'dashboard' ? (
              <h1 className="text-lg font-semibold">外链提交助手</h1>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('dashboard')}
                  className="h-8 w-8 p-0"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </Button>
                <h1 className="text-lg font-semibold">
                  {currentView === 'projects' && '项目管理'}
                  {currentView === 'links' && '外链库'}
                  {currentView === 'forms' && '表单填充'}
                  {currentView === 'settings' && '设置'}
                </h1>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentView('settings')}
                className="h-8 w-8 p-0"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </Button>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mx-4 mt-4">
              <div className="flex items-center justify-between p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
                <span>{error}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearError}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </Button>
              </div>
            </div>
          )}

          {/* 主内容区域 */}
          <div className="flex-1 overflow-auto">
            <div className="p-4">
              {renderCurrentView()}
            </div>
          </div>
        </div>
      </div>

      {/* 对话框 */}
      <ProjectForm
        isOpen={isProjectFormOpen}
        onClose={() => setIsProjectFormOpen(false)}
        project={editingProject}
      />

      <LinkForm
        isOpen={isLinkFormOpen}
        onClose={() => setIsLinkFormOpen(false)}
        link={editingLink}
      />

      <AIOptimizer
        isOpen={isAIOptimizerOpen}
        onClose={() => setIsAIOptimizerOpen(false)}
        onOptimized={() => {}}
      />
    </ToastProvider>
  )
}

export default SidePanel