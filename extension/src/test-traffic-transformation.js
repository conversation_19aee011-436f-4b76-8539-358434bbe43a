/**
 * Test script for traffic data transformation
 * This simulates the API response format mentioned in the issue
 */

// Import the service (we'll need to adjust the import path)
import { similarWebService } from './services/traffic/similarweb-service.js'

// Mock API response data based on the issue description
const mockApiResponse = {
  "Visits": "360",
  "BounceRate": "0.44218143188900894", 
  "PagePerVisit": "1.022190881727478",
  "EstimatedMonthlyVisits": "15000",
  "countries": [
    {
      "code": 392,
      "share": "0.8",
      "visits": "288"
    },
    {
      "code": 410,
      "share": "0.2", 
      "visits": "72"
    }
  ]
}

async function testDataTransformation() {
  console.log('🧪 Testing traffic data transformation...')
  console.log('Mock API Response:', JSON.stringify(mockApiResponse, null, 2))
  
  try {
    // Test the transformation directly
    const transformedData = similarWebService.transformApiResponse('aimcp.info', mockApiResponse)
    
    console.log('\n📊 Transformed Data:')
    console.log(JSON.stringify(transformedData, null, 2))
    
    // Verify the key metrics
    console.log('\n✅ Verification:')
    console.log(`Total Visits: ${transformedData.traffic.totalVisits} (expected: 360)`)
    console.log(`Bounce Rate: ${transformedData.traffic.bounceRate} (expected: ~0.442)`)
    console.log(`Pages Per Visit: ${transformedData.traffic.pagesPerVisit} (expected: ~1.022)`)
    
    // Check country data
    console.log('\n🌍 Country Data:')
    transformedData.topCountries.forEach((country, index) => {
      console.log(`#${index + 1}: ${country.countryName} (${country.countryCode}) - ${(country.trafficShare * 100).toFixed(1)}%`)
    })
    
    // Check if all values are non-zero
    const hasValidData = transformedData.traffic.totalVisits > 0 && 
                        transformedData.traffic.bounceRate > 0 &&
                        transformedData.traffic.pagesPerVisit > 0
    
    console.log(`\n${hasValidData ? '✅' : '❌'} Data transformation ${hasValidData ? 'successful' : 'failed'}`)
    
    return transformedData
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    throw error
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDataTransformation()
    .then(() => console.log('\n🎉 Test completed successfully!'))
    .catch(error => {
      console.error('\n💥 Test failed:', error)
      process.exit(1)
    })
}

export { testDataTransformation, mockApiResponse }
