
import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Gallery from "@/components/blocks/gallery";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import Search from "@/components/blocks/search";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import Editor from "@/components/blocks/editor";
import LinkDashboard from "@/components/blocks/link-dashboard";
import { getLandingPage } from "@/services/page";
import { getUserInfo } from "@/services/user";
import WaitList from "@/components/blocks/waitList";


export async function generateMetadata({
  params,
  searchParams
}: {
  params: Promise<{ locale: string }>;
  searchParams?: Promise<{ tags?: string | string[] }>;
}) {
  const { locale } = await params;
  const searchParamsData = searchParams ? await searchParams : {};
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;

  // If tags param is present, add robots noindex
  const hasTags = searchParamsData && searchParamsData.tags && (Array.isArray(searchParamsData.tags) ? searchParamsData.tags.length > 0 : !!searchParamsData.tags);

  return {
    alternates: {
      canonical: canonicalUrl,
    },
    ...(hasTags && {
      robots: {
        index: false,
        follow: true,
        nocache: true,
      },
    }),
  };
}

export default async function LandingPage({
  params,
  searchParams
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ 
    page?: string; 
    limit?: string; 
    tags?: string | string[]; 
    official?: string; 
    recommended?: string;
    query?: string;
  }>
}) {
  const { locale } = await params;
  const searchParamsData = await searchParams;
  // Check if user is logged in
  if (process.env.NODE_ENV === 'development') {
    console.log("🏠 Landing page: Checking user authentication...");
  }
  const userInfo = await getUserInfo();
  if (process.env.NODE_ENV === 'development') {
    console.log("👤 User info result:", userInfo);
  }
  
  // If user is logged in, show the link dashboard
  if (userInfo?.email) {
    if (process.env.NODE_ENV === 'development') {
      console.log("✅ User authenticated, showing dashboard for:", userInfo.email);
    }
    return <LinkDashboard className="w-full" />;
  }

  if (process.env.NODE_ENV === 'development') {
    console.log("❌ No user found, showing landing page");
  }

  // For non-logged users, show the landing page
  const page = await getLandingPage(locale);
  // Cast the page to include the search and faq properties
  const fullPage = page as typeof page & { 
    search?: any; 
    faq?: any; 
    gallery?: any; 
    editor?: any;
    waitlist?: any;
  };

  // Add locale to searchParams for Item localization
  const enhancedSearchParams = {
    ...searchParamsData,
    locale
  };

  return (
    <>
      {/* {fullPage.hero && <Hero hero={fullPage.hero} />} */}
      {/* {fullPage.search && <Search search={fullPage.search} />} */}
      {/* {fullPage.gallery && <Gallery section={fullPage.gallery} searchParams={enhancedSearchParams} />} */}
      {/* {fullPage.editor && <Editor productName={fullPage.editor.productName} creditCost={fullPage.editor.creditCost} locale={locale} />} */}
      {/* {fullPage.branding && <Branding section={fullPage.branding} />} */}
      {fullPage.waitlist && <WaitList section={fullPage.waitlist} />}
      {/* {fullPage.introduce && <Feature1 section={fullPage.introduce} />}
      {fullPage.benefit && <Feature2 section={fullPage.benefit} />} */}
      {/* {fullPage.usage && <Feature3 section={fullPage.usage} />} */}
      {/* {fullPage.feature && <Feature section={fullPage.feature} />} */}
      {/* {fullPage.showcase && <Showcase section={fullPage.showcase} />} */}
      {/* {fullPage.stats && <Stats section={fullPage.stats} />} */}
      {/* {fullPage.pricing && <Pricing pricing={fullPage.pricing} />} */}
      {/* {fullPage.testimonial && <Testimonial section={fullPage.testimonial} />} */}
      {/* {fullPage.faq && <FAQ section={fullPage.faq} />} */}
      {/* {fullPage.cta && <CTA section={fullPage.cta} />} */}
    </>
  );
}
