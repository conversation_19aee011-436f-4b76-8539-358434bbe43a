module.exports = {
  // Source files to scan (supports glob patterns)
  input: [
    "app/**/*.{js,jsx,ts,tsx}",
    "components/**/*.{js,jsx,ts,tsx}",
    "lib/**/*.{js,jsx,ts,tsx}",
    "hooks/**/*.{js,jsx,ts,tsx}",
    "services/**/*.{js,jsx,ts,tsx}",
    "contexts/**/*.{js,jsx,ts,tsx}",
    "providers/**/*.{js,jsx,ts,tsx}",
    "auth/**/*.{js,jsx,ts,tsx}",
    // Exclude test files and build artifacts
    "!**/*.test.{js,jsx,ts,tsx}",
    "!**/*.spec.{js,jsx,ts,tsx}",
    "!**/node_modules/**",
    "!**/.next/**",
    "!**/build/**",
    "!**/dist/**",
  ],

  // Output directory for translation files
  output: "i18n/messages",

  // Supported locales
  locales: ["en", "zh"],

  // Default locale
  defaultLocale: "en",

  // Additional configuration options
  options: {
    // Enable debug output
    debug: false,
    
    // Function names to scan for
    functionNames: ["t", "getText", ],
    
    // Component names to scan for
    componentNames: ["Trans"],
    
    // Namespace detection
    namespaceDetection: true,
    
    // Key separator
    keySeparator: ".",
    
    // Namespace separator
    namespaceSeparator: ":",
    
    // Default value for missing translations
    defaultValue: "",
    
    // Sort keys alphabetically
    sort: true,
    
    // Remove unused keys
    removeUnusedKeys: false,
    
    // Preserve existing translations
    preserveExisting: true,
  }
};
