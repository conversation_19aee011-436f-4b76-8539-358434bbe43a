module.exports = {
  // Source directory to scan
  sourceDirectory: ".",

  // Output directory for translation files
  outputDirectory: "i18n/messages",

  // Supported locales
  locales: ["en", "zh"],

  // Default locale
  defaultLocale: "en",

  // Files to scan (supports glob patterns)
  pages: [
    {
      match: "**/*.{js,jsx,ts,tsx}",
      ignore: [
        "**/*.test.{js,jsx,ts,tsx}",
        "**/*.spec.{js,jsx,ts,tsx}",
        "**/node_modules/**",
        "**/dist/**",
        "**/build/**",
        "**/.next/**",
        "**/tsconfig.tsbuildinfo",
        "**/dev.log",
        "**/test/**",
        "**/debug/**",
      ],
    },
  ],

  // Global ignore patterns
  ignore: [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/.next/**",
    "**/tsconfig.tsbuildinfo",
    "**/dev.log",
    "**/test/**",
    "**/debug/**",
    "**/lib/browser.ts", // Exclude files that cause read errors
    "**/lib/tools.ts",   // Exclude files that cause read errors
  ],
};
