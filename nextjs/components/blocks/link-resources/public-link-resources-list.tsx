'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { PublicLinkResourceWithStats } from '@/types/links';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ExternalLink, Mail, Star, TrendingUp, Users, Search } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface PublicLinkResourcesListProps {
  category?: string;
  isPaid?: boolean;
  sort?: string;
  search?: string;
  locale: string;
  compact?: boolean;
  page?: number;
}

export function PublicLinkResourcesList({
  category,
  isPaid,
  sort = 'dr_desc',
  search = '',
  locale,
  compact = false,
  page = 1
}: PublicLinkResourcesListProps) {
  const t = useTranslations('linkResources');
  const [resources, setResources] = useState<PublicLinkResourceWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(page);
  const [filters, setFilters] = useState({
    category: category || '',
    isPaid: isPaid,
    sort: sort,
    search: search
  });

  const itemsPerPage = 20;

  const categories = [
    'directory',
    'blog',
    'news',
    'resource-page',
    'guest-post',
    'forum',
    'social-media',
    'press-release',
    'startup-directory',
    'tool-directory'
  ];

  useEffect(() => {
    fetchResources();
  }, [filters, currentPage]);

  const fetchResources = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.category) params.append('category', filters.category);
      if (filters.isPaid !== undefined) params.append('isPaid', filters.isPaid.toString());
      if (filters.sort) params.append('sort', filters.sort);
      if (filters.search) params.append('search', filters.search);
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());
      
      const response = await fetch(`/api/public-link-resources?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setResources(data.resources || data);
        setTotalCount(data.total || data.length);
        setTotalPages(Math.ceil((data.total || data.length) / itemsPerPage));
      }
    } catch (error) {
      console.error('Failed to fetch resources:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('page', newPage.toString());
    window.history.pushState({}, '', url.toString());
  };


  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getDRColor = (dr?: number) => {
    if (!dr) return 'text-muted-foreground';
    if (dr >= 80) return 'text-green-600';
    if (dr >= 60) return 'text-blue-600';
    if (dr >= 40) return 'text-yellow-600';
    if (dr >= 20) return 'text-orange-600';
    return 'text-red-600';
  };

  const getSuccessRateColor = (rate?: number) => {
    if (!rate) return 'text-muted-foreground';
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-blue-600';
    if (rate >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-5/6"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Input
              placeholder={t('filters.searchPlaceholder')}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pr-10"
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2">
              <Search className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
          
          <Select value={filters.category || 'all'} onValueChange={(value) => handleFilterChange('category', value === 'all' ? '' : value)}>
            <SelectTrigger>
              <SelectValue placeholder={t('filters.categoryPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('filters.allCategories')}</SelectItem>
              {categories.map(cat => (
                <SelectItem key={cat} value={cat}>
                  {t(`categories.${cat}`)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select 
            value={filters.isPaid === undefined ? 'all' : filters.isPaid.toString()} 
            onValueChange={(value) => handleFilterChange('isPaid', value === 'all' ? undefined : value === 'true')}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('filters.pricingPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('filters.allPricing')}</SelectItem>
              <SelectItem value="false">{t('filters.free')}</SelectItem>
              <SelectItem value="true">{t('filters.paid')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.sort} onValueChange={(value) => handleFilterChange('sort', value)}>
            <SelectTrigger>
              <SelectValue placeholder={t('filters.sortPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dr_desc">{t('filters.sortByDRDesc')}</SelectItem>
              <SelectItem value="dr_asc">{t('filters.sortByDRAsc')}</SelectItem>
              <SelectItem value="traffic_desc">{t('filters.sortByTrafficDesc')}</SelectItem>
              <SelectItem value="traffic_asc">{t('filters.sortByTrafficAsc')}</SelectItem>
              <SelectItem value="success_rate_desc">{t('filters.sortBySuccessRateDesc')}</SelectItem>
              <SelectItem value="created_desc">{t('filters.sortByNewest')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results */}
      <div className={compact ? "grid grid-cols-1 md:grid-cols-2 gap-6" : "space-y-6"}>
        {resources.length === 0 ? (
          <Card className={compact ? "col-span-full" : ""}>
            <CardContent className="text-center py-12">
              <p className="text-muted-foreground">{t('noResults')}</p>
            </CardContent>
          </Card>
        ) : (
          <>
            {!compact && (
              <div className="text-sm text-muted-foreground mb-4">
                {t('resultsCount', { count: totalCount })} - {t('pagination.page')} {currentPage} {t('pagination.of')} {totalPages}
              </div>
            )}
            
            {resources.map((resource) => (
              <Card key={resource.id} className={`hover:shadow-lg transition-shadow ${compact ? 'h-fit' : ''}`}>
                {compact ? (
                  // Compact Card Layout
                  <>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-base leading-tight mb-1">
                            <a 
                              href={resource.website_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-primary transition-colors truncate block"
                              title={resource.title}
                            >
                              {resource.title}
                            </a>
                          </CardTitle>
                          <div className="flex items-center gap-1 flex-wrap">
                            <Badge variant={resource.is_paid ? 'default' : 'secondary'} className="text-xs">
                              {resource.is_paid ? t('paid') : t('free')}
                            </Badge>
                            {resource.category && (
                              <Badge variant="outline" className="text-xs">
                                {t(`categories.${resource.category.toLowerCase()}`)}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex flex-col items-end gap-1 ml-2">
                          {resource.dr_score && (
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              <span className={`text-xs font-semibold ${getDRColor(resource.dr_score)}`}>
                                {resource.dr_score}
                              </span>
                            </div>
                          )}
                          
                          {resource.traffic > 0 && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Users className="h-3 w-3" />
                              <span>{formatNumber(resource.traffic)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      {resource.description && (
                        <p className="text-sm text-muted-foreground mb-3 overflow-hidden" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical'
                        }}>{resource.description}</p>
                      )}
                      
                      <div className="flex gap-1 flex-wrap">
                        {resource.submission_url && (
                          <Button size="sm" className="text-xs h-7" asChild>
                            <a href={resource.submission_url} target="_blank" rel="noopener noreferrer">
                              {t('actions.submitHere')}
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </a>
                          </Button>
                        )}
                        
                        <Button size="sm" variant="ghost" className="text-xs h-7" asChild>
                          <a href={resource.website_url} target="_blank" rel="noopener noreferrer">
                            {t('actions.visitWebsite')}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                        </Button>
                      </div>
                    </CardContent>
                  </>
                ) : (
                  // Full Card Layout
                  <>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="flex items-center gap-2">
                            <a 
                              href={resource.website_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-primary transition-colors"
                            >
                              {resource.title}
                            </a>
                            <ExternalLink className="h-4 w-4 text-muted-foreground" />
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={resource.is_paid ? 'default' : 'secondary'}>
                              {resource.is_paid ? t('paid') : t('free')}
                            </Badge>
                            {resource.category && (
                              <Badge variant="outline">
                                {t(`categories.${resource.category}`)}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex flex-col items-end gap-2">
                          {resource.dr_score && (
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4" />
                              <span className={`font-semibold ${getDRColor(resource.dr_score)}`}>
                                DR {resource.dr_score}
                              </span>
                            </div>
                          )}
                          
                          {resource.traffic > 0 && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Users className="h-4 w-4" />
                              <span>{formatNumber(resource.traffic)}/mo</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      {resource.description && (
                        <p className="text-muted-foreground mb-4">{resource.description}</p>
                      )}
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="font-medium mb-2">{t('details.submissionMethod')}</h4>
                          <p className="text-sm text-muted-foreground">{resource.submission_method}</p>
                        </div>
                        
                        {resource.response_time && (
                          <div>
                            <h4 className="font-medium mb-2">{t('details.responseTime')}</h4>
                            <p className="text-sm text-muted-foreground">{resource.response_time}</p>
                          </div>
                        )}
                        
                        {resource.success_rate && (
                          <div>
                            <h4 className="font-medium mb-2">{t('details.successRate')}</h4>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4" />
                              <span className={`text-sm font-medium ${getSuccessRateColor(resource.success_rate)}`}>
                                {resource.success_rate}%
                              </span>
                            </div>
                          </div>
                        )}
                        
                        {resource.price_range && resource.is_paid && (
                          <div>
                            <h4 className="font-medium mb-2">{t('details.priceRange')}</h4>
                            <p className="text-sm text-muted-foreground">{resource.price_range}</p>
                          </div>
                        )}
                      </div>

                      {resource.requirements && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">{t('details.requirements')}</h4>
                          <p className="text-sm text-muted-foreground">{resource.requirements}</p>
                        </div>
                      )}

                      <Separator className="my-4" />
                      
                      <div className="flex gap-2">
                        {resource.submission_url && (
                          <Button size="sm" asChild>
                            <a href={resource.submission_url} target="_blank" rel="noopener noreferrer">
                              {t('actions.submitHere')}
                              <ExternalLink className="h-4 w-4 ml-1" />
                            </a>
                          </Button>
                        )}
                        
                        {resource.contact_email && (
                          <Button size="sm" variant="outline" asChild>
                            <a href={`mailto:${resource.contact_email}`}>
                              <Mail className="h-4 w-4 mr-1" />
                              {t('actions.contact')}
                            </a>
                          </Button>
                        )}
                        
                        <Button size="sm" variant="ghost" asChild>
                          <a href={resource.website_url} target="_blank" rel="noopener noreferrer">
                            {t('actions.visitWebsite')}
                            <ExternalLink className="h-4 w-4 ml-1" />
                          </a>
                        </Button>
                      </div>
                    </CardContent>
                  </>
                )}
              </Card>
            ))}
            
            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className={`mt-8 flex justify-center items-center gap-2 ${compact ? 'col-span-full' : ''}`}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  {t('pagination.previous')}
                </Button>
                
                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-10"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  {t('pagination.next')}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}