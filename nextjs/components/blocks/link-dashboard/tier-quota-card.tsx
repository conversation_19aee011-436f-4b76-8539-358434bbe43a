"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Crown, 
  Users, 
  Globe, 
  Link2, 
  TrendingUp, 
  BarChart3,
  Zap,
  ExternalLink 
} from "lucide-react";
import { useTierStatus } from "@/lib/hooks/useTierStatus";
import { useRouter } from "next/navigation";

export function TierQuotaCard() {
  const { tierInfo, loading, isPaidUser, getUsagePercentage, formatUsage } = useTierStatus();
  const router = useRouter();

  if (loading) {
    return (
      <div className="p-4">
        <Card className="animate-pulse">
          <CardContent className="p-4">
            <div className="h-4 bg-muted rounded mb-2"></div>
            <div className="h-8 bg-muted rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-3 bg-muted rounded"></div>
              <div className="h-3 bg-muted rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!tierInfo) {
    return null;
  }

  const handleUpgrade = () => {
    router.push("/subscription");
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return "bg-slate-600";
    if (percentage >= 70) return "bg-slate-500";
    return "bg-slate-400";
  };

  return (
    <div className="p-3 xl:p-4">
      <Card className={`border-2 ${
        isPaidUser
          ? "bg-muted/30 border-primary/30"
          : "bg-muted/20 border-border"
      }`}>
        <CardContent className="p-3">
          {/* Header - More compact */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-1.5">
              {isPaidUser ? (
                <Crown className="h-3 w-3 text-primary" />
              ) : (
                <Zap className="h-3 w-3 text-muted-foreground" />
              )}
              <h4 className="font-semibold text-xs">
                {isPaidUser ? "Pro" : "Free"}
              </h4>
            </div>
            <Badge variant={isPaidUser ? "default" : "secondary"} className="text-xs px-1.5 py-0.5">
              {tierInfo.tier}
            </Badge>
          </div>

          {/* Usage Summary - More compact */}
          <div className="space-y-2 mb-3">
            {/* Projects */}
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-1">
                  <Users className="h-2.5 w-2.5" />
                  <span className="truncate">Projects</span>
                </div>
                <span className="text-muted-foreground text-xs">
                  {formatUsage(tierInfo.limits.projects.used, tierInfo.limits.projects.limit)}
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(tierInfo.limits.projects.used, tierInfo.limits.projects.limit)}
                className="h-1"
              />
            </div>

            {/* Link Resources */}
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-1">
                  <Link2 className="h-2.5 w-2.5" />
                  <span className="truncate">Links</span>
                </div>
                <span className="text-muted-foreground text-xs">
                  {formatUsage(tierInfo.limits.link_resources.used, tierInfo.limits.link_resources.limit)}
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(tierInfo.limits.link_resources.used, tierInfo.limits.link_resources.limit)}
                className="h-1"
              />
            </div>

            {/* Premium Features for Paid Users - Show only most important */}
            {isPaidUser && (
              <>
                {/* DR Queries */}
                {/* <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-2.5 w-2.5" />
                      <span className="truncate">DR Queries</span>
                    </div>
                    <span className="text-muted-foreground text-xs">
                      {formatUsage(tierInfo.limits.monthly_dr_queries.used, tierInfo.limits.monthly_dr_queries.limit)}
                    </span>
                  </div>
                  <Progress 
                    value={getUsagePercentage(tierInfo.limits.monthly_dr_queries.used, tierInfo.limits.monthly_dr_queries.limit)}
                    className="h-1"
                  />
                </div> */}
              </>
            )}
          </div>

          {/* Action Button - More compact */}
          {!isPaidUser ? (
            <Button size="sm" className="w-full h-8 text-xs" onClick={handleUpgrade}>
              <Crown className="h-3 w-3 mr-1" />
              Upgrade
            </Button>
          ) : (
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full h-8 text-xs" 
              onClick={handleUpgrade}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Manage
            </Button>
          )}

          {/* Reset Info for Paid Users - More compact */}
          {isPaidUser && (
            <p className="text-xs text-muted-foreground text-center mt-1.5">
              Resets {new Date(tierInfo.usage_reset_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}