import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";

// Import all locale files statically to avoid dynamic import issues
import enMessages from "./messages/en.json";
import zhMessages from "./messages/zh.json";
// import esMessages from "./messages/es.json";
// import frMessages from "./messages/fr.json";
// import deMessages from "./messages/de.json";
// import jaMessages from "./messages/ja.json";
// import koMessages from "./messages/ko.json";
// import ruMessages from "./messages/ru.json";

const messageMap = {
  en: enMessages,
  zh: zhMessages,
  // es: esMessages,
  // fr: frMessages,
  // de: deMessages,
  // ja: jaMessages,
  // ko: koMessages,
  // ru: ruMessages,
} as const;

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;
  if (!locale || !routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale;
  }

  if (["zh-CN"].includes(locale)) {
    locale = "zh";
  }

  if (!routing.locales.includes(locale as any)) {
    locale = "en";
  }

  try {
    const messages = messageMap[locale as keyof typeof messageMap] || messageMap.en;
    return {
      locale: locale,
      messages: messages as any,
    };
  } catch (e) {
    return {
      locale: "en",
      messages: messageMap.en as any,
    };
  }
});
