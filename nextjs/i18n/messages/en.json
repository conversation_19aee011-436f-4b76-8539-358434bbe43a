{"user": {"sign_in": "Sign In", "sign_out": "Sign Out", "my_orders": "My Orders", "no_auth_or_email": "No authentication or email found"}, "task_succeeded": "Task completed successfully", "file": "File", "status": {"online": "Online", "view_pending": "View Pending", "created": "Created", "no_pending": "No pending items", "error": "Error", "sent": "<PERSON><PERSON>", "title": "Status", "failed": "Failed", "active": "Active", "description": "Description", "inactive": "Inactive", "pending": "Pending", "offline": "Offline"}, "link_type": {}, "user_id": "User ID", "historyLimit": "History Limit", "withStats": "With Statistics", "action": "Action", "table": {"created_at": "Created At", "id": "ID", "actions": "Actions", "name": "Name", "updated_at": "Updated At", "event_type": "Event Type", "status": "Status", "sent_at": "<PERSON><PERSON>", "tools_count": "Tools Count", "type": "Type", "recipient": "Recipient", "subject": "Subject", "item_uuid": "Item UUID"}, "no_image_result": "No image result", "no_logs": "No logs available", "no_templates": "No templates available", "category": "Category", "result_image": "Result Image", "cancel_button": "Cancel", "completed_at": "Completed At", "error_reason": "Error Reason", "my_tasks": "My Tasks", "T": "T", "update_error": "Update Error", "saved_success": "Saved Successfully", "create_error": "Create Error", "details": {}, "selected_items": "Selected Items", "no_json_result": "No JSON result", "searchTerm": "Search Term", "task_status": "Task Status", "type": "Type", "empty": {"no_tools": "No tools available", "no_parameters": "No parameters available", "no_tools_to_translate": "No tools to translate"}, "prev_page": "Previous Page", "error_loading": "Error Loading", "provider": "Provider", "content": "Content", "search": "Search", "task_processing": "Task Processing", "projectId": "Project ID", "create_success": "Created Successfully", "div": "Division", "my_tasks_description": "My Tasks Description", "print_result": "Print Result", "slug": "Slug", "a": "Link", "brief": "Brief", "test": "Test", "sort": "Sort", "timeRange": "Time Range", "query": "Query", "author_avatar_url": "Author <PERSON><PERSON> URL", "saved": "Saved", "tab_json": "JSON Tab", "saved_error": "Save Error", "url": "URL", "update_success": "Updated Successfully", "tip": "Tip", "host": "Host", "task_view": "Task View", "processinfo": "Process Information", "isPaid": "<PERSON>", "q": "Question", "is_recommended": "Is Recommended", "checked_at": "Checked At", "page": "Page", "keywords": "Keywords", "video_urls": "Video URLs", "uuid": "UUID", "tab_image": "Image Tab", "sessionId": "Session ID", "no_tasks": "No tasks available", "goHome": "Go Home", "task_date": "Task Date", "current": "Current", "image_urls": "Image URLs", "create_button": "Create", "tab_text": "Text Tab", "processing_time": "Processing Time", "task_credit_cost": "Task Credit Cost", "cover_url": "Cover URL", "save": "Save", "Authorization": "Authorization", "signin_type": "Sign In Type", "invite_code": "Invite Code", "lang": "Language", "view_task_result": "View Task Result", "refresh_button": "Refresh", "back_to_submissions": "Back to Submissions", "results_title": "Results", "saving": "Saving...", "USD": "USD", "offset": "Offset", "website_url": "Website URL", "task_product": "Task Product", "tagName": "Tag Name", "task_pending": "Task Pending", "stdio": "Standard I/O", "sse": "Server-Sent Events", "includeHistory": "Include History", "is_official": "Is Official", "task_result": "Task Result", "title": "Title", "limit": "Limit", "process_button": "Process", "next_page": "Next Page", "author_name": "Author Name", "locale": "Locale", "update_button": "Update", "description": "Description", "field": "Field", "api_key": "API Key", "ai_summary": "AI Summary", "domain": {"management": {"title": "Domain Management", "description": "Manage domain expiration dates and avoid missing renewals", "addDomain": "Add Domain", "editDomain": "Edit Domain", "deleteDomain": "Delete Domain", "refreshWhois": "Refresh WHOIS", "bulkImport": "Bulk Import", "associateProject": "Associate Project", "removeAssociation": "Remove Association", "searchPlaceholder": "Search domains...", "noDomains": "No domains found", "loading": "Loading...", "confirmDelete": "Are you sure you want to delete this domain?", "confirmDeleteMessage": "This action cannot be undone.", "domainAdded": "Domain added successfully", "domainUpdated": "Domain updated successfully", "domainDeleted": "Domain deleted successfully", "whoisRefreshed": "WHOIS data refreshed successfully", "errorAddingDomain": "Error adding domain", "errorUpdatingDomain": "Error updating domain", "errorDeletingDomain": "Error deleting domain", "errorRefreshingWhois": "Error refreshing WHOIS data", "expirationTracking": "Track expiration dates to avoid missing renewals"}, "status": {"active": "Active", "expired": "Expired", "expiring": "Expiring Soon", "unknown": "Unknown"}, "stats": {"total": "Total Domains", "active": "Active", "expiring": "Expiring Soon", "expired": "Expired", "projects": "Associated Projects"}, "form": {"domain": "Domain", "addDescription": "Enter domain details. WHOIS data will be fetched automatically.", "registrar": "Registrar", "dnsProvider": "DNS Provider", "createdDate": "Created Date", "expiryDate": "Expiry Date", "registrationPrice": "Registration Price", "renewalPrice": "Renewal Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "autoRenew": "Auto Renew", "monitorExpiry": "Monitor Expiry", "alertDaysBefore": "<PERSON><PERSON> Before", "notes": "Notes", "tags": "Tags", "isFavorite": "Favorite", "nameServers": "Name Servers", "required": "Required", "optional": "Optional", "enterDomain": "Enter domain name", "enterRegistrar": "Enter registrar name", "enterDnsProvider": "Enter DNS provider", "enterNotes": "Enter notes", "enterTags": "Enter tags (comma separated)", "selectCurrency": "Select currency", "selectProject": "Select project", "daysBeforeExpiry": "Days before expiry"}, "filter": {"all": "All", "status": "Status", "registrar": "Registrar", "dnsProvider": "DNS Provider", "project": "Project", "expiring": "Expiring Soon", "favorites": "Favorites"}, "sort": {"domain": "Domain", "expiryDate": "Expiry Date", "createdDate": "Created Date", "registrar": "Registrar", "ascending": "Ascending", "descending": "Descending"}, "table": {"domain": "Domain", "registrar": "Registrar", "dnsProvider": "DNS Provider", "status": "Status", "expiryDate": "Expiry Date", "createdDate": "Created Date", "projects": "Projects", "actions": "Actions", "daysUntilExpiry": "Days Until Expiry", "whoisLastUpdated": "WHOIS Updated", "expires": "Expires", "renewalPrice": "Renewal Price", "never": "Never", "view": "View", "edit": "Edit", "delete": "Delete", "refresh": "Refresh", "associate": "Associate", "favorite": "Favorite", "unfavorite": "Unfavorite"}, "whois": {"data": "WHOIS Data", "lastUpdated": "Last Updated", "cacheExpires": "<PERSON><PERSON> Expires", "refreshing": "Refreshing...", "noData": "No WHOIS data available", "expired": "WHOIS data expired", "clickToRefresh": "Click to refresh WHOIS data"}, "project": {"association": "Project Association", "associatedProjects": "Associated Projects", "noAssociatedProjects": "No associated projects", "selectProject": "Select a project to associate", "isPrimary": "Primary domain for project", "create_project": "Create Project", "edit_project": "Edit Project", "associationAdded": "Project association added", "associationRemoved": "Project association removed", "errorAddingAssociation": "Error adding project association", "errorRemovingAssociation": "Error removing project association"}, "monitoring": {"title": "Domain Expiration Monitoring", "expiryMonitoring": "Expiry Monitoring", "alertSettings": "<PERSON><PERSON>", "lastAlertSent": "Last <PERSON><PERSON>", "noAlerts": "No alerts sent yet", "alertsEnabled": "<PERSON><PERSON><PERSON>", "alertsDisabled": "<PERSON><PERSON><PERSON> Disabled", "preventMissedRenewals": "Prevent missed renewals with expiration alerts"}, "import": {"title": "Bulk Import Domains", "description": "Import multiple domains at once", "format": "Format: domain.com,registrar,dns-provider", "example": "Example: example.com,Go<PERSON><PERSON>dy,Cloudflare", "paste": "Paste domains (one per line)", "import": "Import Domains", "importing": "Importing...", "success": "Successfully imported {count} domains", "error": "Error importing domains", "invalidFormat": "Invalid format on line {line}", "duplicateDomain": "Duplicate domain: {domain}"}}, "task_failed_status": "Task Failed", "no_text_result": "No text result", "invitation": {"invites_count": "<PERSON><PERSON><PERSON>", "earn_credits": "<PERSON><PERSON><PERSON>", "share_title": "Share Invitation", "credits_per_invite": "Credits per Invite", "copy": "Copy", "share_text": "Share this invitation link with your friends!", "copy_success": "Copied to clipboard!", "how_it_works": "How It Works", "your_code": "Your Code", "copying": "Copying...", "share_success": "Shared successfully!", "share_description": "Invite friends and earn credits", "share_code": "Share Code", "title": "Invitation System", "share_invite_link": "Share Invite Link", "copy_failed": "Co<PERSON> failed", "share": "Share", "share_failed": "Share failed", "successful_invites": "Successful Invites"}, "editor": {"result_image": "Result Image", "copied_json": "JSON copied to clipboard!", "results_title": "Results", "copy": "Copy", "usage_processing_time": "Processing Time", "my_tasks": "My Tasks", "usage_supported_formats": "Supported Formats", "upload_description": "Upload your file for processing", "drop_files": "Drop files here to upload", "tab_text": "Text", "no_json_result": "No JSON result", "no_text_result": "No text result", "processing_file": "Processing file...", "processing": "Processing...", "file_selected": "File selected", "error_polling": "Error polling for results", "unexpected_error": "Unexpected error occurred", "usage_max_file_size": "Maximum file size", "task_complete": "Task completed", "no_image_result": "No image result", "usage_tips_title": "Usage Tips", "error_no_file": "No file selected", "process_file": "Process File", "error": "Error", "tab_json": "JSON", "usage_sign_in": "Sign in to use this feature", "error_file_size": "File size exceeds limit", "usage_credit_cost": "Credit cost", "error_file_type": "Unsupported file type", "task_timeout": "Task timeout", "error_create_task": "Error creating task", "upload_title": "Upload File", "copied_text": "Text copied to clipboard!", "upload_process_prompt": "Upload and process your file", "file_type": "File type", "remove_file": "Remove file", "results_description": "Processing results", "tab_image": "Image", "max_file_size": "Maximum file size", "unknown_error": "Unknown error", "task_failed": "Task failed"}, "linkResources": {"title": "External Link Resource Library", "description": "Manage your collection of external link resources and track their effectiveness for your projects", "loading": "Loading...", "noResults": "No resources found", "resultsCount": "{count} resources found", "paid": "Paid", "free": "Free", "common": {"home": "Home"}, "seo": {"title": "External Link Resource Management", "description": "Your database of verified external link platforms with performance tracking", "keywords": "external link resources, link library, project management, SEO tools, link building", "categoryDescription": "Browse {category} backlink resources in your library", "paidDescription": "Premium backlink resources in your collection", "freeDescription": "Free backlink resources in your collection", "page": "Page", "additionalTitle": "How Your Personal Resource Library Works", "howItWorks": "How It Works", "howItWorksDesc": "Your systematic approach to managing backlink resources", "step1": "Build your personal library of verified submission platforms", "step2": "Track performance metrics and success rates", "step3": "Update resource status based on project results", "step4": "Maintain quality through regular review and updates", "benefits": "Benefits", "benefitsDesc": "Why maintain your personal resource library", "benefit1": "Curated collection tailored to your projects", "benefit2": "Track success rates and ROI for each resource", "benefit3": "Avoid wasting time on low-quality platforms", "benefit4": "Build institutional knowledge over time"}, "stats": {"totalResources": "Total Resources in Library", "categories": "Categories Tracked", "highAuthority": "Average Success Rate"}, "features": {"title": "Personal Library Features", "highQuality": "Quality Tracking", "highQualityDesc": "Monitor performance and success rates", "verified": "Personal Verification", "verifiedDesc": "Track your own experience with each platform", "qualityAssured": "Continuous Updates", "qualityAssuredDesc": "Keep your library current and effective", "updated": "Performance Monitoring", "updatedDesc": "Track ROI and effectiveness over time"}, "filters": {"searchPlaceholder": "Search your resources...", "categoryPlaceholder": "Select category", "allCategories": "All Categories", "pricingPlaceholder": "Select pricing", "allPricing": "All Pricing", "free": "Free Only", "paid": "Paid Only", "sortPlaceholder": "Sort by", "sortByDRDesc": "Domain Rating (High to Low)", "sortByDRAsc": "Domain Rating (Low to High)", "sortByTrafficDesc": "Traffic (High to Low)", "sortByTrafficAsc": "Traffic (Low to High)", "sortBySuccessRateDesc": "Success Rate (High to Low)", "sortByNewest": "Recently Added"}, "categories": {"directory": "Directory", "blog": "Blog", "news": "News", "resource-page": "Resource Page", "guest-post": "Guest Post", "forum": "Forum", "social-media": "Social Media", "press-release": "Press Release", "startup-directory": "Startup Directory", "tool-directory": "Tool Directory"}, "details": {"submissionMethod": "Submission Method", "responseTime": "Response Time", "successRate": "Personal Success Rate", "priceRange": "Price Range", "requirements": "Requirements", "lastUsed": "Last Used", "personalNotes": "Personal Notes"}, "actions": {"submitHere": "Submit Here", "visitWebsite": "Visit Website", "contact": "Contact", "updateStatus": "Update Status", "addNotes": "Add Notes"}, "pagination": {"page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "faq": {"title": "Frequently Asked Questions", "q1": "What is a personal backlink resource library?", "a1": "A curated collection of backlink platforms that you've personally tested and verified for your side projects.", "q2": "How do I track resource performance?", "a2": "Record success rates, response times, and personal notes for each platform you use.", "q3": "Can I import existing resources?", "a3": "Yes, you can bulk import your existing backlink resources and add performance data.", "q4": "How often should I update my library?", "a4": "Review and update your library weekly based on new submissions and results.", "q5": "What metrics should I track?", "a5": "Track success rates, response times, costs, and personal effectiveness notes.", "q6": "How does this help my side projects?", "a6": "Maintain a quality resource library to efficiently build backlinks for future projects."}, "filters.searchPlaceholder": "filters_searchPlaceholder", "filters.categoryPlaceholder": "filters_categoryPlaceholder", "filters.allCategories": "filters_allCategories", "categories.": "categories_", "filters.pricingPlaceholder": "filters_pricingPlaceholder", "filters.allPricing": "filters_allPricing", "filters.free": "filters_free", "filters.paid": "filters_paid", "filters.sortPlaceholder": "filters_sortPlaceholder", "filters.sortByDRDesc": "filters_sortByDRDesc", "filters.sortByDRAsc": "filters_sortByDRAsc", "filters.sortByTrafficDesc": "filters_sortByTrafficDesc", "filters.sortByTrafficAsc": "filters_sortByTrafficAsc", "filters.sortBySuccessRateDesc": "filters_sortBySuccessRateDesc", "filters.sortByNewest": "filters_sortByNewest", "pagination.page": "pagination_page", "pagination.of": "pagination_of", "actions.submitHere": "actions_submitHere", "actions.visitWebsite": "actions_visitWebsite", "details.submissionMethod": "details_submissionMethod", "details.responseTime": "details_responseTime", "details.successRate": "details_successRate", "details.priceRange": "details_priceRange", "details.requirements": "details_requirements", "actions.contact": "actions_contact", "pagination.previous": "pagination_previous", "pagination.next": "pagination_next"}, "links": {"traffic": "Traffic", "title": "Links", "add": "Add Link", "saving": "Saving...", "cancel": "Cancel", "update": "Update", "a": "Link", "dr_score": "Domain Rating", "add_link": "Add Link", "add_link_description": "Add a new link to your project", "link": {"basic_info": "Basic Information", "title": "Title"}, "project": {"create_project": "Create a new project to organize your links", "name": "Project name", "domain": "example.com (will be normalized automatically)", "domain_note": "Domain will be automatically normalized (www prefix removed, lowercased)", "description": "Optional description of your project", "category": "Project Category", "select_category": "Select or create category", "category_help": "Select a category for the project for easy organization and management", "analytics_platform": "Analytics Platform (Optional)", "analytics_note": "You can configure analytics integration later from the Integrations tab", "actions": {"cancel": "Cancel", "create": "Create"}}, "import.success": "import_success", "import.title": "import_title", "import.description": "import_description", "import.csv_format": "import_csv_format", "import.required_columns": "import_required_columns", "import.optional_columns": "import_optional_columns", "import.download_template": "import_download_template", "import.upload_file": "import_upload_file", "import.validation_errors": "import_validation_errors", "actions.cancel": "actions_cancel", "import.importing": "import_importing", "actions.import": "actions_import", "link.edit": "link_edit", "link.edit_description": "link_edit_description", "link.basic_info": "link_basic_info", "link.url": "link_url", "link.url_normalization_note": "link_url_normalization_note", "link.title": "link_title", "link.title_placeholder": "link_title_placeholder", "link.submit_url": "link_submit_url", "link.submit_url_placeholder": "link_submit_url_placeholder", "link.submit_url_description": "link_submit_url_description", "link.type_and_cost": "link_type_and_cost", "link.link_type": "link_link_type", "link_type.free": "link_type_free", "link_type.paid": "link_type_paid", "link.price": "link_price", "link.currency": "link_currency", "link.source_and_acquisition": "link_source_and_acquisition", "link.source": "link_source", "link.source_placeholder": "link_source_placeholder", "link.acquisition_method": "link_acquisition_method", "link.acquisition_method_placeholder": "link_acquisition_method_placeholder", "link.performance_metrics": "link_performance_metrics", "link.dr_score": "link_dr_score", "link.monthly_traffic": "link_monthly_traffic", "link.notes_section": "link_notes_section", "link.notes": "link_notes", "link.notes_placeholder": "link_notes_placeholder", "project.edit_project": "project_edit_project", "project.create_project": "project_create_project", "project.name": "project_name", "project.domain": "project_domain", "project.description": "project_description", "create": "create"}, "SubmitPage": {"heading": "Add Resource to Library", "home": "Home", "submit": "Add Resource", "title": "Add Resource", "description": "Add a new backlink resource to your personal library"}, "SearchPage": {"search": "Search", "title": "Search Resources", "description": "Search your personal backlink resource library", "home": "Home"}, "metadata": {"title": "MyBackLinks - External Link Resource Management & Project Analytics", "description": "Manage your external link resource library, track project DR values and backlinks, monitor domain expiration, and integrate with analytics platforms", "keywords": "external link library, project analytics, domain management, DR tracking, SEMrush integration, plausible analytics"}, "NotFound": {"title": "Page Not Found", "description": "The page you're looking for doesn't exist or has been moved.", "goHome": "Go Home"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to access your personal backlink library, side project tracking, and domain management tools", "cancel_title": "Cancel", "google_sign_in": "Google", "github_sign_in": "GitHub"}, "integrations": "Integrations", "projects": {"title": "Projects", "description": "Track DR values, backlinks, and analytics for your projects", "addProject": "Add Project", "editProject": "Edit Project", "deleteProject": "Delete Project", "viewAnalytics": "View Analytics", "projectUrl": "Project URL", "projectName": "Project Name", "projectDescription": "Project Description", "noProjects": "No projects found", "createFirst": "Create your first project to start tracking", "drTracking": {"title": "Domain Rating Tracking", "currentDR": "Current DR", "drHistory": "DR History", "lastUpdated": "Last Updated", "uploadSemrush": "Upload SEMrush File", "manualUpdate": "Manual Update", "limitedQueries": "Limited manual queries available"}, "backlinks": {"title": "Backlink Tracking", "totalBacklinks": "Total Backlinks", "newBacklinks": "New Backlinks", "lostBacklinks": "Lost Backlinks", "referringDomains": "Referring Domains", "backlinkSources": "Backlink Sources", "qualityScore": "Quality Score", "noDeadLinkTracking": "Note: Dead link tracking not available"}, "analytics": {"title": "Project Analytics", "traffic": "Traffic Overview", "backlinks": "Backlinks", "domains": "Associated Domains", "lastUpdated": "Last Updated (Weekly)", "plausibleData": "Plausible Analytics", "googleAnalyticsData": "Google Analytics", "googleConsoleData": "Google Search Console", "trafficSources": "Traffic Sources", "topPages": "Top Pages", "keywords": "Keywords", "noData": "No analytics data available", "weeklyUpdates": "Data updated weekly"}, "resourceLibrary": {"title": "Resource Library Updates", "description": "Update your backlink resource library based on this project's results", "addSuccessful": "Add Successful Resources", "markUnsuccessful": "<PERSON>successful Resources", "updateNotes": "Update Resource Notes", "trackPerformance": "Track Performance"}, "limits": {"freeProjects": "Free users can track up to 5 projects", "freeUpdateFrequency": "Analytics and DR data updated weekly", "freeManualQueries": "10 manual DR queries per month", "premiumProjects": "Premium users can track up to 1000 projects", "premiumUpdateFrequency": "Analytics and DR data updated weekly", "premiumManualQueries": "100 manual DR queries per month", "upgradePrompt": "Upgrade to Premium for more projects and manual queries", "noRealTimeTracking": "Real-time tracking not available", "noApiAccess": "API access not provided", "noDataExport": "Data export not available"}}, "analytics": {"title": "Analytics", "traffic": {"title": "Traffic Analytics", "description": "Track your website traffic from integrated analytics platforms", "plausible": "Plausible Analytics", "googleAnalytics": "Google Analytics", "googleConsole": "Google Search Console", "visitors": "Visitors", "pageViews": "Page Views", "sessions": "Sessions", "bounceRate": "Bounce Rate", "avgSessionDuration": "Average Session Duration", "topReferrers": "Top Referrers", "topCountries": "Top Countries", "searchQueries": "Search Queries", "clicks": "<PERSON>licks", "impressions": "Impressions", "ctr": "Click-through Rate", "avgPosition": "Average Position", "weeklyUpdates": "Data updated weekly", "notRealTime": "Not real-time data"}, "backlinks": {"title": "Backlink Analytics", "description": "Monitor your backlink profile and growth (via SEMrush import or manual queries)", "totalBacklinks": "Total Backlinks", "newBacklinks": "New Backlinks", "lostBacklinks": "Lost Backlinks", "referringDomains": "Referring Domains", "domainRating": "Domain Rating", "urlRating": "URL Rating", "anchorText": "Anchor Text", "linkType": "Link Type", "status": "Status", "firstSeen": "First Seen", "lastSeen": "Last Seen", "semrushImport": "SEMrush File Import", "manualQuery": "Manual Query", "queryLimits": "Query limits apply", "noDeadLinkTracking": "Dead link tracking not available"}, "integration": {"title": "Analytics Integration", "description": "Connect your analytics providers to track data", "plausible": {"title": "Plausible Analytics", "description": "Connect your Plausible Analytics account for traffic data", "apiKey": "API Key", "siteId": "Site ID", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect", "disconnect": "Disconnect"}, "googleAnalytics": {"title": "Google Analytics", "description": "Connect your Google Analytics account for traffic insights", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect with Google", "disconnect": "Disconnect", "selectProperty": "Select Property"}, "googleConsole": {"title": "Google Search Console", "description": "Connect your Google Search Console account for search data", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect with Google", "disconnect": "Disconnect", "selectProperty": "Select Property"}, "limitations": {"title": "Integration Limitations", "weeklyUpdates": "Data updated weekly, not real-time", "noApiAccess": "API access not provided", "noDataExport": "Data export not available"}}}, "pricing": {"title": "Pricing", "free": {"title": "Free Plan", "price": "Free", "features": {"0": "Up to 5 projects", "1": "External link resource library", "2": "Basic DR tracking (10 manual queries/month)", "3": "Domain expiration monitoring", "4": "Analytics integration (Plausible, Google)", "5": "Weekly data updates"}, "limitations": {"0": "Limited to 5 projects", "1": "10 manual DR queries per month", "2": "Weekly data updates only", "3": "No API access", "4": "No data export", "5": "No dead link tracking"}}, "premium": {"title": "Premium Plan", "price": "$29/month", "features": {"0": "Up to 1000 projects", "1": "Advanced resource library management", "2": "Enhanced DR tracking (100 manual queries/month)", "3": "Advanced domain monitoring with alerts", "4": "Priority analytics integration", "5": "Weekly data updates", "6": "Priority support", "7": "Advanced reporting"}, "upgrade": "Upgrade to Premium", "benefits": {"0": "More manual DR queries", "1": "Track up to 1000 projects", "2": "Advanced domain alerts", "3": "Priority customer support"}, "limitations": {"0": "Still weekly updates (not real-time)", "1": "No API access", "2": "No data export", "3": "No dead link tracking"}}}, "limitations": {"title": "Platform Limitations", "description": "Understanding what MyBackLinks does and doesn't support", "noDeadLinkTracking": {"title": "No Dead Link Tracking", "description": "Automatic dead link detection and monitoring is not available"}, "noApiAccess": {"title": "No API Access", "description": "API endpoints for external integrations are not provided"}, "noDataExport": {"title": "No Data Export", "description": "Bulk data export functionality is not available"}, "weeklyUpdates": {"title": "Weekly Data Updates", "description": "Data is updated approximately once per week, not in real-time"}, "manualQueries": {"title": "Limited Manual Queries", "description": "Manual DR and backlink queries are limited based on your plan"}}, "features": {"title": "Core Features", "description": "What MyBackLinks helps you accomplish", "personalLibrary": {"title": "Personal Backlink Resource Library", "description": "Maintain and organize your collection of backlink submission platforms"}, "projectTracking": {"title": "Side Project Tracking", "description": "Track DR values and backlinks for your side projects via SEMrush import or manual queries"}, "resourceMaintenance": {"title": "Resource Library Maintenance", "description": "Update your resource library based on project results and performance"}, "domainManagement": {"title": "Domain Expiration Management", "description": "Monitor domain expiration dates and avoid missing renewals"}, "analyticsIntegration": {"title": "Analytics Integration", "description": "Connect with Plausible, Google Analytics, and Google Search Console for traffic insights"}}, "subscription": {"title": "Subscription Management", "description": "Manage your MyBackLinks subscription and view usage statistics", "current_plan": "Current Plan", "usage_summary": "Usage Summary", "upgrade_plan": "Upgrade Plan", "manage_billing": "Manage Billing", "features": "Features", "limits": "Limits", "projects": "Projects", "domains": "Domains", "link_resources": "Link Resources", "dr_queries": "DR Queries", "traffic_updates": "Traffic Updates", "free_tier": "Free Tier", "professional_tier": "Professional Tier", "unlimited": "Unlimited", "per_month": "per month", "upgrade_now": "Upgrade Now", "contact_support": "Contact Support"}, "blog": {"title": "Blog", "description": "Latest insights and updates from our team", "read_more_text": "Read More"}, "blocks": {"hero": {"title": "Build Your Ultimate External Link Resource Library", "description": "Track website traffic, monitor domain ratings, and manage your external link portfolio for better project promotion and growth.", "announcement": {"title": "New Analytics Dashboard Available", "url": "/dashboard"}, "buttons": {"primary": "Start Building Your Library", "secondary": "View Live Demo"}, "tip": "Free plan includes up to 5 projects and basic analytics integration"}, "feature1": {"title": "Real-Time Traffic & DR Analytics", "description": "Monitor your side projects' performance with comprehensive analytics integration and domain rating tracking to optimize your growth strategy.", "features": {"traffic_monitoring": {"title": "Traffic Analytics Integration", "description": "Connect with Google Analytics, Plausible, and Google Search Console for unified traffic insights across all your projects."}, "dr_tracking": {"title": "Domain Rating Monitoring", "description": "Track DR changes over time via SEMrush integration or manual queries to measure your SEO progress effectively."}, "backlink_analysis": {"title": "Backlink Portfolio Management", "description": "Monitor new and lost backlinks, analyze referring domains, and track the quality of your link building efforts."}, "performance_insights": {"title": "Growth Performance Insights", "description": "Get actionable insights on traffic trends, keyword rankings, and backlink opportunities to accelerate project growth."}}}, "feature2": {"title": "Smart External Link Resource Management", "description": "Build and maintain your personal database of high-quality backlink opportunities with performance tracking and success rate monitoring.", "features": {"resource_library": {"title": "Curated Resource Database", "description": "Organize external link opportunities by category, track success rates, and build your personalized submission workflow."}, "performance_tracking": {"title": "Success Rate Analytics", "description": "Monitor response times, approval rates, and ROI for each resource to optimize your outreach strategy over time."}, "quality_management": {"title": "Quality Assurance System", "description": "Rate and review platforms based on your experience, maintaining a high-quality resource library for future campaigns."}}}, "feature": {"title": "Complete Domain Portfolio Management", "description": "Never miss a domain renewal again with automated expiration monitoring and comprehensive domain management tools.", "features": {"expiration_monitoring": {"title": "Expiration Alerts", "description": "Get timely notifications before domain expiration to prevent service interruptions and protect your brand."}, "whois_tracking": {"title": "WHOIS Data Management", "description": "Automatically track registrar information, DNS settings, and renewal costs for all your domains in one place."}, "project_association": {"title": "Project Integration", "description": "Link domains to specific projects for holistic tracking of your digital asset portfolio and performance metrics."}}}, "pricing": {"title": "Choose Your Growth Plan", "description": "Start free and scale with your projects. No hidden fees, cancel anytime.", "plans": {"free": {"name": "Starter", "price": "Free", "description": "Perfect for testing and small projects", "features": ["Up to 5 projects tracking", "External link resource library", "Basic DR tracking (10 queries/month)", "Domain expiration monitoring", "Analytics integration", "Weekly data updates"], "button": "Get Started Free", "tip": "No credit card required"}, "premium": {"name": "Professional", "price": "$29", "unit": "/month", "description": "Ideal for serious side project builders", "features": ["Up to 1000 projects tracking", "Advanced resource management", "Enhanced DR tracking (100 queries/month)", "Advanced domain monitoring", "Priority analytics integration", "Weekly data updates", "Priority support", "Advanced reporting"], "button": "Upgrade to Pro", "popular": true}}}, "testimonials": {"title": "Loved by Side Project Builders", "description": "See how creators are growing their projects with our platform"}, "faq": {"title": "Frequently Asked Questions", "description": "Everything you need to know about building your external link resource library", "questions": {"what_is_platform": {"question": "What is MyBackLinks and how does it help my side projects?", "answer": "MyBackLinks helps you build and maintain a personal library of external link opportunities while tracking your projects' performance metrics like traffic, DR, and backlinks."}, "how_track_dr": {"question": "How do you track domain rating and backlinks?", "answer": "We integrate with SEMrush for data import and provide manual query options. You can upload SEMrush reports or use our limited manual queries based on your plan."}, "analytics_integration": {"question": "Which analytics platforms do you support?", "answer": "We integrate with Google Analytics, Google Search Console, and Plausible Analytics to provide unified traffic insights for your projects."}, "resource_library": {"question": "How does the external link resource library work?", "answer": "You can organize and track external link opportunities by category, monitor success rates, and build your personalized outreach workflow for better results."}, "data_updates": {"question": "How often is data updated?", "answer": "Analytics and DR data are updated approximately once per week. We don't provide real-time tracking to keep costs manageable and focus on trends."}, "plan_limits": {"question": "What are the differences between free and premium plans?", "answer": "Free users can track up to 5 projects with 10 manual DR queries per month. Premium users get up to 1000 projects with 100 manual queries and priority support."}}}, "cta": {"title": "Ready to Grow Your Side Project Empire?", "description": "Join thousands of creators building sustainable external link libraries and tracking their project success.", "button": {"primary": "Start Building for Free", "secondary": "View Live Demo"}}}, "faq": {"helpful_information": "Helpful information", "still_have_questions": "Still have questions?", "contact_support_description": "We're here to help you build your external link empire", "contact_support": "Contact Support"}, "metadata.title": "metadata_title", "metadata.description": "metadata_description", "metadata.keywords": "metadata_keywords", "tabs.all": "tabs_all", "tabs.pending": "tabs_pending", "tabs.sent": "tabs_sent", "tabs.failed": "tabs_failed", "table.id": "table_id", "table.recipient": "table_recipient", "table.subject": "table_subject", "table.event_type": "table_event_type", "table.status": "table_status", "table.created_at": "table_created_at", "table.sent_at": "table_sent_at", "table.actions": "table_actions", "events.": "events_", "pagination.previous": "pagination_previous", "pagination.page_info": "pagination_page_info", "pagination.next": "pagination_next", "status.pending": "status_pending", "status.sent": "status_sent", "status.failed": "status_failed", "templates.title": "templates_title", "templates.description": "templates_description", "templates.content": "templates_content", "templates.view_button": "templates_view_button", "logs.title": "logs_title", "logs.description": "logs_description", "logs.content": "logs_content", "logs.view_button": "logs_view_button", "process.title": "process_title", "process.description": "process_description", "process.content": "process_content", "process.action_button": "process_action_button", "status.title": "status_title", "status.description": "status_description", "status.error": "status_error", "status.no_pending": "status_no_pending", "status.view_pending": "status_view_pending", "variable_help.title": "variable_help_title", "variable_help.description": "variable_help_description", "variable_help.item_variables": "variable_help_item_variables", "variable_help.item_name": "variable_help_item_name", "variable_help.item_author": "variable_help_item_author", "variable_help.item_url": "variable_help_item_url", "variable_help.item_link": "variable_help_item_link", "variable_help.recipient_variables": "variable_help_recipient_variables", "variable_help.recipient_name": "variable_help_recipient_name", "variable_help.recipient_email": "variable_help_recipient_email", "variable_help.site_url": "variable_help_site_url", "variable_help.date": "variable_help_date", "types.item_submitted": "types_item_submitted", "types.item_published": "types_item_published", "types.item_rejected": "types_item_rejected", "tabs.active": "tabs_active", "tabs.inactive": "tabs_inactive", "table.name": "table_name", "table.updated_at": "table_updated_at", "types.": "types_", "status.active": "status_active", "status.inactive": "status_inactive", "tabs.overview": "tabs_overview", "tabs.traffic": "tabs_traffic", "stats.total_items": "stats_total_items", "stats.public": "stats_public", "stats.submissions": "stats_submissions", "stats.pending": "stats_pending", "stats.localizations": "stats_localizations", "stats.translations": "stats_translations", "stats.total_clicks": "stats_total_clicks", "stats.engagement": "stats_engagement", "traffic.today_visitors": "traffic_today_visitors", "traffic.today_pageviews": "traffic_today_pageviews", "traffic.weekly_visitors": "traffic_weekly_visitors", "traffic.weekly_pageviews": "traffic_weekly_pageviews", "traffic.top_pages": "traffic_top_pages", "traffic.top_pages_desc": "traffic_top_pages_desc", "traffic.views": "traffic_views", "actions.submissions": "actions_submissions", "actions.submissions_desc": "actions_submissions_desc", "actions.go_to_submissions": "actions_go_to_submissions", "actions.pending_review": "actions_pending_review", "actions.manage": "actions_manage", "actions.manage_desc": "actions_manage_desc", "actions.go_to_items": "actions_go_to_items", "actions.tags": "actions_tags", "actions.tags_desc": "actions_tags_desc", "actions.go_to_tags": "actions_go_to_tags", "actions.tools": "actions_tools", "actions.tools_desc": "actions_tools_desc", "actions.go_to_tools": "actions_go_to_tools", "tag_groups.title": "tag_groups_title", "tags_list.description": "tags_list_description", "actions.back": "actions_back", "edit.title": "edit_title", "edit.description": "edit_description", "tabs.tools": "tabs_tools", "tabs.translations": "tabs_translations", "edit.tools_title": "edit_tools_title", "edit.tools_description": "edit_tools_description", "edit.translations_title": "edit_translations_title", "edit.translations_description": "edit_translations_description", "edit.danger_zone": "edit_danger_zone", "edit.danger_description": "edit_danger_description", "table.item_uuid": "table_item_uuid", "table.type": "table_type", "table.tools_count": "table_tools_count", "actions.edit": "actions_edit", "tools.title": "tools_title", "tools.description": "tools_description", "tools.add_new": "tools_add_new", "error.title": "error_title", "error.description": "error_description", "tools.no_data_title": "tools_no_data_title", "tools.no_data_description": "tools_no_data_description", "translations.title": "translations_title", "translations.description": "translations_description", "translations.how_it_works_title": "translations_how_it_works_title", "translations.how_it_works_desc1": "translations_how_it_works_desc1", "translations.how_it_works_desc2": "translations_how_it_works_desc2", "translations.supported_languages_title": "translations_supported_languages_title", "translations.translation_tips_title": "translations_translation_tips_title", "translations.tip1": "translations_tip1", "translations.tip2": "translations_tip2", "translations.tip3": "translations_tip3", "columns.brief": "columns_brief", "columns.processinfo": "columns_processinfo", "columns.name": "columns_name", "columns.actions": "columns_actions", "filter.all": "filter_all", "status.online": "status_online", "status.offline": "status_offline", "status.created": "status_created", "filter.allLocales": "filter_allLocales", "settings.title": "settings_title", "settings.invitation.title": "settings_invitation_title", "settings.invitation.description": "settings_invitation_description", "settings.invitation.credits_label": "settings_invitation_credits_label", "settings.invitation.credits_description": "settings_invitation_credits_description", "api_keys.create_api_key": "api_keys_create_api_key", "api_keys.title": "api_keys_title", "api_keys.form.name": "api_keys_form_name", "api_keys.form.name_placeholder": "api_keys_form_name_placeholder", "api_keys.form.submit": "api_keys_form_submit", "api_keys.tip": "api_keys_tip", "api_keys.table.name": "api_keys_table_name", "api_keys.table.key": "api_keys_table_key", "api_keys.table.created_at": "api_keys_table_created_at", "api_keys.no_api_keys": "api_keys_no_api_keys", "invitation.title": "invitation_title", "invitation.your_code": "invitation_your_code", "invitation.share_code": "invitation_share_code", "invitation.credits_per_invite": "invitation_credits_per_invite", "invitation.earn_credits": "invitation_earn_credits", "invitation.invites_count": "invitation_invites_count", "invitation.successful_invites": "invitation_successful_invites", "invitation.share_invite_link": "invitation_share_invite_link", "invitation.share_description": "invitation_share_description", "invitation.how_it_works": "invitation_how_it_works", "user.my_orders": "user_my_orders", "my_credits.title": "my_credits_title", "my_credits.left_tip": "my_credits_left_tip", "my_credits.recharge": "my_credits_recharge", "my_credits.table.trans_no": "my_credits_table_trans_no", "my_credits.table.trans_type": "my_credits_table_trans_type", "my_credits.table.credits": "my_credits_table_credits", "my_credits.table.updated_at": "my_credits_table_updated_at", "my_credits.no_credits": "my_credits_no_credits", "favorites.confirm_delete": "favorites_confirm_delete", "common.error_occurred": "common_error_occurred", "favorites.deleted": "favorites_deleted", "common.delete": "common_delete", "favorites.title": "favorites_title", "favorites.no_description": "favorites_no_description", "common.view": "common_view", "favorites.empty_title": "favorites_empty_title", "favorites.empty_description": "favorites_empty_description", "favorites.browse_items": "favorites_browse_items", "my_orders.table.order_no": "my_orders_table_order_no", "my_orders.table.email": "my_orders_table_email", "my_orders.table.product_name": "my_orders_table_product_name", "my_orders.table.amount": "my_orders_table_amount", "my_orders.table.paid_at": "my_orders_table_paid_at", "my_orders.title": "my_orders_title", "my_orders.description": "my_orders_description", "my_orders.no_orders": "my_orders_no_orders", "user.no_auth_or_email": "user_no_auth_or_email", "errors.failed_to_fetch_submissions": "errors_failed_to_fetch_submissions", "my_submit.title": "my_submit_title", "my_submit.tip": "my_submit_tip", "my_submit.submit_new_item": "my_submit_submit_new_item", "my_submit.table.name": "my_submit_table_name", "my_submit.table.website_url": "my_submit_table_website_url", "my_submit.table.status": "my_submit_table_status", "my_submit.table.submitted_at": "my_submit_table_submitted_at", "my_submit.table.processed_at": "my_submit_table_processed_at", "my_submit.table.approved_at": "my_submit_table_approved_at", "my_submit.table.rejected_at": "my_submit_table_rejected_at", "my_submit.no_submissions": "my_submit_no_submissions", "subscription.title": "subscription_title", "subscription.description": "subscription_description", "subscription.current_plan": "subscription_current_plan", "subscription.usage_summary": "subscription_usage_summary", "subscription.upgrade_plan": "subscription_upgrade_plan", "subscription.manage_billing": "subscription_manage_billing", "subscription.features": "subscription_features", "subscription.limits": "subscription_limits", "subscription.projects": "subscription_projects", "subscription.domains": "subscription_domains", "subscription.link_resources": "subscription_link_resources", "subscription.dr_queries": "subscription_dr_queries", "subscription.traffic_updates": "subscription_traffic_updates", "subscription.free_tier": "subscription_free_tier", "subscription.professional_tier": "subscription_professional_tier", "subscription.unlimited": "subscription_unlimited", "subscription.per_month": "subscription_per_month", "subscription.upgrade_now": "subscription_upgrade_now", "subscription.contact_support": "subscription_contact_support", "tools.introduction": "tools_introduction", "tools.usage_examples": "tools_usage_examples", "tools.no_introduction": "tools_no_introduction", "tools.no_tools": "tools_no_tools", "tools.no_usage_examples": "tools_no_usage_examples", "seo.title": "seo_title", "seo.description": "seo_description", "categories.": "categories_", "seo.categoryDescription": "seo_categoryDescription", "paid": "paid", "seo.paidDescription": "seo_paidDescription", "free": "free", "seo.freeDescription": "seo_freeDescription", "seo.page": "seo_page", "seo.keywords": "seo_keywords", "common.home": "common_home", "stats.totalResources": "stats_totalResources", "stats.categories": "stats_categories", "stats.highAuthority": "stats_highAuthority", "features.title": "features_title", "features.highQuality": "features_highQuality", "features.highQualityDesc": "features_highQualityDesc", "features.verified": "features_verified", "features.verifiedDesc": "features_verifiedDesc", "features.qualityAssured": "features_qualityAssured", "features.qualityAssuredDesc": "features_qualityAssuredDesc", "features.updated": "features_updated", "features.updatedDesc": "features_updatedDesc", "loading": "loading", "faq.title": "faq_title", "faq.q1": "faq_q1", "faq.a1": "faq_a1", "faq.q2": "faq_q2", "faq.a2": "faq_a2", "faq.q3": "faq_q3", "faq.a3": "faq_a3", "faq.q4": "faq_q4", "faq.a4": "faq_a4", "faq.q5": "faq_q5", "faq.a5": "faq_a5", "faq.q6": "faq_q6", "faq.a6": "faq_a6", "seo.additionalTitle": "seo_additionalTitle", "seo.howItWorks": "seo_howItWorks", "seo.howItWorksDesc": "seo_howItWorksDesc", "seo.step1": "seo_step1", "seo.step2": "seo_step2", "seo.step3": "seo_step3", "seo.benefits": "seo_benefits", "seo.benefitsDesc": "seo_benefitsDesc", "seo.benefit1": "seo_benefit1", "seo.benefit2": "seo_benefit2", "seo.benefit3": "seo_benefit3", "seo.benefit4": "seo_benefit4", "blog.title": "blog_title", "blog.description": "blog_description", "blog.read_more_text": "blog_read_more_text", "home": "home", "submit": "submit", "heading": "heading", "admin": {"emails": {"process": {"process.success": "process_success", "process.no_emails": "process_no_emails", "process.error": "process_error", "form.batch_size": "form_batch_size", "form.processing": "form_processing", "form.process_button": "form_process_button", "form.result_title": "form_result_title", "form.result_processed": "form_result_processed", "form.result_success": "form_result_success", "form.result_failed": "form_result_failed"}, "templates": {"form": {"update_success": "update_success", "create_success": "create_success", "update_error": "update_error", "create_error": "create_error", "name.label": "name_label", "name.placeholder": "name_placeholder", "event_type.label": "event_type_label", "event_type.placeholder": "event_type_placeholder", "event_type.item_submitted": "event_type_item_submitted", "event_type.item_published": "event_type_item_published", "event_type.item_rejected": "event_type_item_rejected", "event_type.cannot_change": "event_type_cannot_change", "subject.label": "subject_label", "subject.placeholder": "subject_placeholder", "body.label": "body_label", "body.placeholder": "body_placeholder", "body.help": "body_help", "is_active.label": "is_active_label", "cancel_button": "cancel_button", "saving": "saving", "update_button": "update_button", "create_button": "create_button"}}}, "settings": {"validation.not_a_number": "validation_not_a_number", "validation.min_value": "validation_min_value", "validation.max_value": "validation_max_value", "saved_success": "saved_success", "saved_error": "saved_error", "saving": "saving", "saved": "saved", "save": "save"}, "mcps": {"tools": {"edit.confirm_delete": "edit_confirm_delete", "success.deleted": "success_deleted", "error.delete_failed": "error_delete_failed", "actions.deleting": "actions_deleting", "actions.delete": "actions_delete", "validation.tool_name_required": "validation_tool_name_required", "validation.tool_description_required": "validation_tool_description_required", "validation.param_name_required": "validation_param_name_required", "validation.param_type_required": "validation_param_type_required", "success.saved": "success_saved", "error.save_failed": "error_save_failed", "error.title": "error_title", "fields.type": "fields_type", "placeholders.select_type": "placeholders_select_type", "fields.allow_public": "fields_allow_public", "fields.public": "fields_public", "fields.private": "fields_private", "sections.tools": "sections_tools", "actions.add_tool": "actions_add_tool", "empty.no_tools": "empty_no_tools", "placeholders.unnamed_tool": "placeholders_unnamed_tool", "fields.tool_name": "fields_tool_name", "placeholders.tool_name": "placeholders_tool_name", "fields.updated_at": "fields_updated_at", "fields.description": "fields_description", "fields.default_language": "fields_default_language", "placeholders.tool_description": "placeholders_tool_description", "sections.parameters": "sections_parameters", "actions.add_parameter": "actions_add_parameter", "empty.no_parameters": "empty_no_parameters", "fields.param_name": "fields_param_name", "placeholders.param_name": "placeholders_param_name", "fields.param_type": "fields_param_type", "fields.param_description": "fields_param_description", "placeholders.param_description": "placeholders_param_description", "fields.required": "fields_required", "actions.saving": "actions_saving", "actions.save": "actions_save", "success.translations_saved": "success_translations_saved", "translations.edit_title": "translations_edit_title", "translations.select_language": "translations_select_language", "translations.current_language": "translations_current_language", "translations.editing_language": "translations_editing_language", "empty.no_tools_to_translate": "empty_no_tools_to_translate", "translations.translated": "translations_translated", "translations.not_translated": "translations_not_translated", "translations.tool_description": "translations_tool_description", "translations.original": "translations_original", "placeholders.translation": "placeholders_translation", "translations.parameters": "translations_parameters", "translations.parameter_description": "translations_parameter_description", "translations.no_description": "translations_no_description", "placeholders.parameter_translation": "placeholders_parameter_translation", "actions.save_translations": "actions_save_translations"}}}, "domain.loadError": "domain_loadError", "domain.whoisRefreshed": "domain_whoisRefreshed", "domain.whoisFresh": "domain_whoisFresh", "domain.refreshError": "domain_refreshError", "domain.status.unknown": "domain_status_unknown", "domain.status.expired": "domain_status_expired", "domain.status.expiring": "domain_status_expiring", "domain.status.active": "domain_status_active", "domain.table.edit": "domain_table_edit", "domain.table.refresh": "domain_table_refresh", "domain.table.delete": "domain_table_delete", "domain.table.registrar": "domain_table_registrar", "domain.table.expiryDate": "domain_table_expiryDate", "domain.table.renewalPrice": "domain_table_renewalPrice", "domain.management.title": "domain_management_title", "domain.management.description": "domain_management_description", "domain.stats.total": "domain_stats_total", "domain.stats.active": "domain_stats_active", "domain.stats.expiring": "domain_stats_expiring", "domain.stats.expired": "domain_stats_expired", "domain.stats.projects": "domain_stats_projects", "domain.management.addDomain": "domain_management_addDomain", "domain.form.addDescription": "domain_form_addDescription", "domain.form.registrationPrice": "domain_form_registrationPrice", "domain.form.renewalPrice": "domain_form_renewalPrice", "domain.management.searchPlaceholder": "domain_management_searchPlaceholder", "domain.management.noDomains": "domain_management_noDomains", "domain.empty.searchDescription": "domain_empty_searchDescription", "domain.empty.description": "domain_empty_description", "domain.table.domain": "domain_table_domain", "domain.table.status": "domain_table_status", "domain.table.expires": "domain_table_expires", "domain.table.projects": "domain_table_projects", "domain.table.actions": "domain_table_actions", "user.sign_out": "user_sign_out", "user.sign_in": "user_sign_in", "sign_modal.sign_in_title": "sign_modal_sign_in_title", "sign_modal.sign_in_description": "sign_modal_sign_in_description", "sign_modal.google_sign_in": "sign_modal_google_sign_in", "sign_modal.github_sign_in": "sign_modal_github_sign_in", "sign_modal.cancel_title": "sign_modal_cancel_title"}