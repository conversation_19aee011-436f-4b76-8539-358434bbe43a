# i18n Tool Migration Guide

## Overview

This guide documents the migration from the slow Python-based i18n management script to the fast Node.js-based `next-intl-scanner` solution.

## Performance Comparison

| Tool | Language | Time | Performance |
|------|----------|------|-------------|
| **manage_i18n.py** | Python | >106 seconds | Baseline |
| **next-intl-scanner** | Node.js | 1.4 seconds | **75x faster** |
| **i18next-scanner** | Node.js | 1.2 seconds | **88x faster** |

## Why the Migration?

### Problems with the Python Script

1. **Extremely slow performance** - Takes over 106 seconds for 13,369 files
2. **Sequential processing** - No parallelization
3. **Memory intensive** - Loads entire files into memory
4. **Complex regex operations** - Multiple regex patterns on full file content
5. **Poor scalability** - Performance degrades with codebase size

### Benefits of next-intl-scanner

1. **Blazing fast** - 75x performance improvement
2. **AST-based parsing** - More accurate than regex
3. **Streaming architecture** - Memory efficient
4. **TypeScript native** - Better TypeScript support
5. **Watch mode** - Real-time updates during development
6. **next-intl specific** - Designed for this exact use case

## Migration Steps

### 1. Automatic Migration

Run the migration script:

```bash
cd nextjs
./scripts/migrate-i18n-tools.sh
```

This script will:
- Backup existing translation files
- Install next-intl-scanner
- Extract translations with the new tool
- Compare results
- Update package.json scripts

### 2. Manual Migration

If you prefer manual steps:

```bash
# Install the new tool
pnpm add next-intl-scanner --save-dev

# Create configuration file
cp next-intl-scanner.config.js.example next-intl-scanner.config.js

# Run extraction
npx next-intl-scanner extract

# Add scripts to package.json
npm pkg set scripts.extract-translations="next-intl-scanner extract"
npm pkg set scripts.extract-translations:watch="next-intl-scanner extract --watch"
```

## Configuration

### next-intl-scanner Configuration

The `next-intl-scanner.config.js` file controls the extraction behavior:

```javascript
module.exports = {
  // Source files to scan
  input: [
    "app/**/*.{js,jsx,ts,tsx}",
    "components/**/*.{js,jsx,ts,tsx}",
    // ... other directories
    "!**/*.test.{js,jsx,ts,tsx}", // Exclude tests
  ],
  
  // Output directory
  output: "i18n/messages",
  
  // Supported locales
  locales: ["en", "zh"],
  
  // Default locale
  defaultLocale: "en",
  
  options: {
    // Function names to scan for
    functionNames: ["t", "getText"],
    
    // Sort keys alphabetically
    sort: true,
    
    // Preserve existing translations
    preserveExisting: true,
  }
};
```

## New Workflow

### Development Workflow

1. **Watch mode during development:**
   ```bash
   pnpm run extract-translations:watch
   ```

2. **Manual extraction:**
   ```bash
   pnpm run extract-translations
   ```

3. **Check for issues:**
   ```bash
   pnpm run i18n:check
   ```

### Build Integration

Update your build process in `package.json`:

```json
{
  "scripts": {
    "build": "next-intl-scanner extract && next build",
    "extract-translations": "next-intl-scanner extract",
    "extract-translations:watch": "next-intl-scanner extract --watch"
  }
}
```

## Feature Comparison

| Feature | Python Script | next-intl-scanner | Status |
|---------|---------------|-------------------|---------|
| Key extraction | ✅ | ✅ | ✅ Maintained |
| Namespace support | ✅ | ✅ | ✅ Maintained |
| Multiple locales | ✅ | ✅ | ✅ Maintained |
| File synchronization | ✅ | ✅ | ✅ Maintained |
| AI translation | ✅ | ✅ | ✅ Maintained |
| Watch mode | ❌ | ✅ | 🎉 New feature |
| Performance | ❌ | ✅ | 🚀 75x faster |
| TypeScript support | ⚠️ | ✅ | 🎉 Improved |

## Troubleshooting

### Common Issues

1. **Missing translations after migration**
   - Check the backup directory created during migration
   - Verify the configuration includes all source directories
   - Run with debug mode: `npx next-intl-scanner extract --debug`

2. **TypeScript parsing errors**
   - Update the configuration to handle modern TypeScript features
   - Check file extensions in the input patterns

3. **Performance still slow**
   - Verify you're using next-intl-scanner, not the Python script
   - Check for large files that might need exclusion

### Rollback Plan

If issues arise, you can rollback:

```bash
# Restore from backup
cp -r i18n-backup-YYYYMMDD-HHMMSS/* i18n/

# Remove new tool
pnpm remove next-intl-scanner

# Use Python script temporarily
python3 ../../tools/scripts/manage_i18n.py check nextjs i18n/messages
```

## Next Steps

1. **Test thoroughly** - Verify all translations work correctly
2. **Update CI/CD** - Replace Python script calls with new commands
3. **Train team** - Share new workflow with developers
4. **Monitor performance** - Enjoy the 75x speed improvement!
5. **Clean up** - Remove Python script when confident

## Support

For issues with:
- **next-intl-scanner**: [GitHub Issues](https://github.com/mohamedali0887/next-intl-scanner/issues)
- **Migration process**: Contact the development team
- **Performance problems**: Check this guide's troubleshooting section
